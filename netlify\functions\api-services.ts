// Services API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, serviceSchema, paginationSchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 100, 15 * 60 * 1000)
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod
    const pathSegments = event.path.split('/').filter(Boolean)
    const serviceId = pathSegments[pathSegments.length - 1]

    switch (method) {
      case 'GET':
        if (serviceId && serviceId !== 'services' && !isNaN(Number(serviceId))) {
          return await getService(db, Number(serviceId), rateLimit)
        }
        return await getServices(event, db, rateLimit)
      
      case 'POST':
        return await createService(event, context, db, rateLimit)
      
      case 'PUT':
        if (!serviceId || isNaN(Number(serviceId))) {
          return createErrorResponse(400, 'Service ID is required for updates')
        }
        return await updateService(event, context, db, Number(serviceId), rateLimit)
      
      case 'DELETE':
        if (!serviceId || isNaN(Number(serviceId))) {
          return createErrorResponse(400, 'Service ID is required for deletion')
        }
        return await deleteService(context, db, Number(serviceId), rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Services API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getServices(event: any, db: any, rateLimit: any) {
  try {
    const queryParams = event.queryStringParameters || {}
    
    // Validate pagination parameters
    const paginationValidation = validateInput(paginationSchema, queryParams)
    if (!paginationValidation.success) {
      return createErrorResponse(400, 'Invalid query parameters', paginationValidation.errors)
    }

    const { page = 1, limit = 10, sort = 'desc', featured } = paginationValidation.data
    const offset = (page - 1) * limit

    // Build query
    let whereClause = 'WHERE is_available = 1'
    let params: any[] = []
    
    if (featured !== undefined) {
      whereClause += ' AND is_featured = ?'
      params.push(featured)
    }

    const orderBy = sort === 'asc' ? 'ASC' : 'DESC'
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM services ${whereClause}`
    const countResult = await db.prepare(countQuery).bind(...params).first()
    const total = countResult?.total || 0

    // Get services
    const query = `
      SELECT * FROM services 
      ${whereClause}
      ORDER BY is_featured DESC, display_order ASC, created_at ${orderBy}
      LIMIT ? OFFSET ?
    `
    const services = await db.prepare(query).bind(...params, limit, offset).all()

    // Parse JSON fields
    const result = services.map((service: any) => ({
      ...service,
      deliverables: service.deliverables ? JSON.parse(service.deliverables) : [],
      features: service.features ? JSON.parse(service.features) : [],
      is_featured: Boolean(service.is_featured),
      is_available: Boolean(service.is_available)
    }))

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      })
    }
  } catch (error) {
    console.error('Get services error:', error)
    return createErrorResponse(500, 'Failed to fetch services', error)
  }
}

async function getService(db: any, id: number, rateLimit: any) {
  try {
    const service = await db
      .prepare('SELECT * FROM services WHERE id = ? AND is_available = 1')
      .bind(id)
      .first()

    if (!service) {
      return createErrorResponse(404, 'Service not found')
    }

    // Parse JSON fields
    const result = {
      ...service,
      deliverables: service.deliverables ? JSON.parse(service.deliverables) : [],
      features: service.features ? JSON.parse(service.features) : [],
      is_featured: Boolean(service.is_featured),
      is_available: Boolean(service.is_available)
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    }
  } catch (error) {
    console.error('Get service error:', error)
    return createErrorResponse(500, 'Failed to fetch service', error)
  }
}

async function createService(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(serviceSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        INSERT INTO services (
          name, description, short_description, price_range, price_from, price_to,
          currency, icon_name, category, duration, deliverables, features,
          is_featured, is_available, display_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?)
      `)
      .bind(
        data.name,
        data.description || null,
        data.short_description || null,
        data.price_range || null,
        data.price_from || null,
        data.price_to || null,
        data.currency || 'USD',
        data.icon_name || null,
        data.category || null,
        data.duration || null,
        data.deliverables ? JSON.stringify(data.deliverables) : null,
        data.features ? JSON.stringify(data.features) : null,
        data.is_featured || false,
        data.is_available !== false,
        now,
        now
      )
      .run()

    if (!result.success) {
      return createErrorResponse(500, 'Failed to create service')
    }

    // Fetch created service
    const created = await db
      .prepare('SELECT * FROM services WHERE id = ?')
      .bind(result.meta?.last_row_id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...created,
          deliverables: created.deliverables ? JSON.parse(created.deliverables) : [],
          features: created.features ? JSON.parse(created.features) : [],
          is_featured: Boolean(created.is_featured),
          is_available: Boolean(created.is_available)
        },
        message: 'Service created successfully'
      })
    }
  } catch (error) {
    console.error('Create service error:', error)
    return createErrorResponse(500, 'Failed to create service', error)
  }
}

async function updateService(event: any, context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(serviceSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        UPDATE services SET
          name = ?, description = ?, short_description = ?, price_range = ?,
          price_from = ?, price_to = ?, currency = ?, icon_name = ?, category = ?,
          duration = ?, deliverables = ?, features = ?, is_featured = ?,
          is_available = ?, updated_at = ?
        WHERE id = ?
      `)
      .bind(
        data.name,
        data.description || null,
        data.short_description || null,
        data.price_range || null,
        data.price_from || null,
        data.price_to || null,
        data.currency || 'USD',
        data.icon_name || null,
        data.category || null,
        data.duration || null,
        data.deliverables ? JSON.stringify(data.deliverables) : null,
        data.features ? JSON.stringify(data.features) : null,
        data.is_featured || false,
        data.is_available !== false,
        now,
        id
      )
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Service not found')
    }

    // Fetch updated service
    const updated = await db
      .prepare('SELECT * FROM services WHERE id = ?')
      .bind(id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...updated,
          deliverables: updated.deliverables ? JSON.parse(updated.deliverables) : [],
          features: updated.features ? JSON.parse(updated.features) : [],
          is_featured: Boolean(updated.is_featured),
          is_available: Boolean(updated.is_available)
        },
        message: 'Service updated successfully'
      })
    }
  } catch (error) {
    console.error('Update service error:', error)
    return createErrorResponse(500, 'Failed to update service', error)
  }
}

async function deleteService(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM services WHERE id = ?')
      .bind(id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Service not found')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Service deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete service error:', error)
    return createErrorResponse(500, 'Failed to delete service', error)
  }
}
