import React, { createContext, useContext, useEffect, useState } from 'react'
import netlifyIdentity from 'netlify-identity-widget'
import { User, AuthState } from '@/types'

interface AuthContextType extends AuthState {
  login: () => void
  logout: () => void
  signup: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  })

  useEffect(() => {
    // Initialize Netlify Identity
    netlifyIdentity.init({
      APIUrl: import.meta.env.VITE_NETLIFY_IDENTITY_URL || `${window.location.origin}/.netlify/identity`,
    })

    // Check if user is already logged in
    const currentUser = netlifyIdentity.currentUser()
    if (currentUser) {
      setAuthState({
        user: transformNetlifyUser(currentUser),
        isAuthenticated: true,
        isLoading: false,
        error: null,
      })
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }))
    }

    // Set up event listeners
    netlifyIdentity.on('init', (user) => {
      if (user) {
        setAuthState({
          user: transformNetlifyUser(user),
          isAuthenticated: true,
          isLoading: false,
          error: null,
        })
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }))
      }
    })

    netlifyIdentity.on('login', (user) => {
      setAuthState({
        user: transformNetlifyUser(user),
        isAuthenticated: true,
        isLoading: false,
        error: null,
      })
      netlifyIdentity.close()
    })

    netlifyIdentity.on('logout', () => {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      })
    })

    netlifyIdentity.on('error', (err) => {
      console.error('Netlify Identity error:', err)
      setAuthState(prev => ({
        ...prev,
        error: err.message || 'Authentication error',
        isLoading: false,
      }))
    })

    return () => {
      netlifyIdentity.off('init')
      netlifyIdentity.off('login')
      netlifyIdentity.off('logout')
      netlifyIdentity.off('error')
    }
  }, [])

  const login = () => {
    setAuthState(prev => ({ ...prev, error: null }))
    netlifyIdentity.open('login')
  }

  const logout = () => {
    netlifyIdentity.logout()
  }

  const signup = () => {
    setAuthState(prev => ({ ...prev, error: null }))
    netlifyIdentity.open('signup')
  }

  const value: AuthContextType = {
    ...authState,
    login,
    logout,
    signup,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

function transformNetlifyUser(netlifyUser: any): User {
  return {
    id: netlifyUser.id,
    email: netlifyUser.email,
    role: netlifyUser.app_metadata?.roles?.[0] || 'user',
    created_at: netlifyUser.created_at,
    updated_at: netlifyUser.updated_at,
  }
}
