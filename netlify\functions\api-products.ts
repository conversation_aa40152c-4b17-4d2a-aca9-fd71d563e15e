// Products API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, productSchema, paginationSchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 100, 15 * 60 * 1000)
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod
    const pathSegments = event.path.split('/').filter(Boolean)
    const productId = pathSegments[pathSegments.length - 1]

    switch (method) {
      case 'GET':
        if (productId && productId !== 'products' && !isNaN(Number(productId))) {
          return await getProduct(db, Number(productId), rateLimit)
        }
        return await getProducts(event, db, rateLimit)
      
      case 'POST':
        return await createProduct(event, context, db, rateLimit)
      
      case 'PUT':
        if (!productId || isNaN(Number(productId))) {
          return createErrorResponse(400, 'Product ID is required for updates')
        }
        return await updateProduct(event, context, db, Number(productId), rateLimit)
      
      case 'DELETE':
        if (!productId || isNaN(Number(productId))) {
          return createErrorResponse(400, 'Product ID is required for deletion')
        }
        return await deleteProduct(context, db, Number(productId), rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Products API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getProducts(event: any, db: any, rateLimit: any) {
  try {
    const queryParams = event.queryStringParameters || {}
    
    // Validate pagination parameters
    const paginationValidation = validateInput(paginationSchema, queryParams)
    if (!paginationValidation.success) {
      return createErrorResponse(400, 'Invalid query parameters', paginationValidation.errors)
    }

    const { page = 1, limit = 10, sort = 'desc', featured } = paginationValidation.data
    const offset = (page - 1) * limit

    // Build query
    let whereClause = 'WHERE is_available = 1'
    let params: any[] = []
    
    if (featured !== undefined) {
      whereClause += ' AND is_featured = ?'
      params.push(featured)
    }

    const orderBy = sort === 'asc' ? 'ASC' : 'DESC'
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`
    const countResult = await db.prepare(countQuery).bind(...params).first()
    const total = countResult?.total || 0

    // Get products
    const query = `
      SELECT * FROM products 
      ${whereClause}
      ORDER BY is_featured DESC, display_order ASC, created_at ${orderBy}
      LIMIT ? OFFSET ?
    `
    const products = await db.prepare(query).bind(...params, limit, offset).all()

    // Parse JSON fields
    const result = products.map((product: any) => ({
      ...product,
      gallery_urls: product.gallery_urls ? JSON.parse(product.gallery_urls) : [],
      tags: product.tags ? JSON.parse(product.tags) : [],
      features: product.features ? JSON.parse(product.features) : [],
      is_featured: Boolean(product.is_featured),
      is_available: Boolean(product.is_available)
    }))

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      })
    }
  } catch (error) {
    console.error('Get products error:', error)
    return createErrorResponse(500, 'Failed to fetch products', error)
  }
}

async function getProduct(db: any, id: number, rateLimit: any) {
  try {
    const product = await db
      .prepare('SELECT * FROM products WHERE id = ? AND is_available = 1')
      .bind(id)
      .first()

    if (!product) {
      return createErrorResponse(404, 'Product not found')
    }

    // Parse JSON fields
    const result = {
      ...product,
      gallery_urls: product.gallery_urls ? JSON.parse(product.gallery_urls) : [],
      tags: product.tags ? JSON.parse(product.tags) : [],
      features: product.features ? JSON.parse(product.features) : [],
      is_featured: Boolean(product.is_featured),
      is_available: Boolean(product.is_available)
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    }
  } catch (error) {
    console.error('Get product error:', error)
    return createErrorResponse(500, 'Failed to fetch product', error)
  }
}

async function createProduct(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(productSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        INSERT INTO products (
          name, description, short_description, price, currency, purchase_link,
          demo_link, github_link, category, tags, features, is_featured,
          is_available, display_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?)
      `)
      .bind(
        data.name,
        data.description || null,
        data.short_description || null,
        data.price || null,
        data.currency || 'USD',
        data.purchase_link || null,
        data.demo_link || null,
        data.github_link || null,
        data.category || null,
        data.tags ? JSON.stringify(data.tags) : null,
        data.features ? JSON.stringify(data.features) : null,
        data.is_featured || false,
        data.is_available !== false,
        now,
        now
      )
      .run()

    if (!result.success) {
      return createErrorResponse(500, 'Failed to create product')
    }

    // Fetch created product
    const created = await db
      .prepare('SELECT * FROM products WHERE id = ?')
      .bind(result.meta?.last_row_id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...created,
          gallery_urls: created.gallery_urls ? JSON.parse(created.gallery_urls) : [],
          tags: created.tags ? JSON.parse(created.tags) : [],
          features: created.features ? JSON.parse(created.features) : [],
          is_featured: Boolean(created.is_featured),
          is_available: Boolean(created.is_available)
        },
        message: 'Product created successfully'
      })
    }
  } catch (error) {
    console.error('Create product error:', error)
    return createErrorResponse(500, 'Failed to create product', error)
  }
}

async function updateProduct(event: any, context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(productSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        UPDATE products SET
          name = ?, description = ?, short_description = ?, price = ?, currency = ?,
          purchase_link = ?, demo_link = ?, github_link = ?, category = ?, tags = ?,
          features = ?, is_featured = ?, is_available = ?, updated_at = ?
        WHERE id = ?
      `)
      .bind(
        data.name,
        data.description || null,
        data.short_description || null,
        data.price || null,
        data.currency || 'USD',
        data.purchase_link || null,
        data.demo_link || null,
        data.github_link || null,
        data.category || null,
        data.tags ? JSON.stringify(data.tags) : null,
        data.features ? JSON.stringify(data.features) : null,
        data.is_featured || false,
        data.is_available !== false,
        now,
        id
      )
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Product not found')
    }

    // Fetch updated product
    const updated = await db
      .prepare('SELECT * FROM products WHERE id = ?')
      .bind(id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...updated,
          gallery_urls: updated.gallery_urls ? JSON.parse(updated.gallery_urls) : [],
          tags: updated.tags ? JSON.parse(updated.tags) : [],
          features: updated.features ? JSON.parse(updated.features) : [],
          is_featured: Boolean(updated.is_featured),
          is_available: Boolean(updated.is_available)
        },
        message: 'Product updated successfully'
      })
    }
  } catch (error) {
    console.error('Update product error:', error)
    return createErrorResponse(500, 'Failed to update product', error)
  }
}

async function deleteProduct(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM products WHERE id = ?')
      .bind(id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Product not found')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Product deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete product error:', error)
    return createErrorResponse(500, 'Failed to delete product', error)
  }
}
