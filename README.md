# Cinmana Personal Website & Desktop CMS

A comprehensive personal website with a companion desktop CMS application, built with React, TypeScript, Tailwind CSS, Netlify Functions, and Electron.

## 🚀 Features

### Personal Website
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Dynamic Content**: Biography, certificates, products, services, and testimonials
- **Modern Stack**: React 18 + TypeScript + Vite
- **SEO Optimized**: Meta tags, Open Graph, and semantic HTML
- **Performance**: Optimized images, lazy loading, and code splitting

### Desktop CMS Application
- **Cross-Platform**: Built with Electron for Windows, macOS, and Linux
- **Real-time Sync**: Instant updates with the website backend
- **User-Friendly**: Intuitive interface for content management
- **Secure**: Netlify Identity authentication integration
- **Offline Capable**: Local data caching and sync when online

### Backend & Infrastructure
- **Serverless**: Netlify Functions for API endpoints
- **Database**: Netlify D1 (SQLite) for data storage
- **Authentication**: Netlify Identity for secure access
- **File Storage**: Integrated file upload and management
- **Rate Limiting**: Built-in API protection

## 📋 Prerequisites

- Node.js 18+ and npm 9+
- Git
- Netlify account (free tier)
- Code editor (VS Code recommended)

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd cinmana
npm install
```

### 2. Environment Configuration

Copy the example environment file:
```bash
cp .env.example .env
```

Update `.env` with your Netlify site information:
```env
VITE_NETLIFY_SITE_URL=https://your-site.netlify.app
VITE_NETLIFY_IDENTITY_URL=https://your-site.netlify.app/.netlify/identity
VITE_API_BASE_URL=/.netlify/functions
```

### 3. Netlify Setup

#### Deploy to Netlify
1. Push your code to GitHub/GitLab
2. Connect your repository to Netlify
3. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Functions directory: `netlify/functions`

#### Enable Netlify Identity
1. Go to Site Settings > Identity
2. Enable Identity
3. Set registration to "Invite only"
4. Configure external providers if needed
5. Add admin role in Identity > Settings

#### Setup Netlify D1 Database
1. Go to Site Settings > Environment Variables
2. The D1 database will be automatically configured
3. Run database migrations on first deploy

### 4. Local Development

Start the development server:
```bash
npm run dev
```

Start Netlify Functions locally:
```bash
npm run dev-functions
```

Start Electron CMS (in another terminal):
```bash
npm run electron-dev
```

## 🏗️ Project Structure

```
cinmana/
├── src/                    # React frontend source
│   ├── components/         # Reusable UI components
│   ├── pages/             # Page components
│   ├── hooks/             # Custom React hooks
│   ├── store/             # State management
│   ├── api/               # API client functions
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── netlify/
│   └── functions/         # Serverless API functions
├── electron/              # Electron desktop app
├── database/              # Database schema and migrations
├── public/                # Static assets
└── docs/                  # Documentation
```

## 🔧 Available Scripts

### Development
- `npm run dev` - Start Vite development server
- `npm run dev-functions` - Start Netlify Functions locally
- `npm run electron-dev` - Start Electron in development mode

### Building
- `npm run build` - Build for production
- `npm run build-functions` - Build Netlify Functions
- `npm run package-electron` - Build Electron app

### Testing & Quality
- `npm run test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run type-check` - TypeScript type checking

## 🚀 Deployment

### Website Deployment
The website automatically deploys to Netlify when you push to the main branch.

### Desktop CMS Distribution
Build the Electron app:
```bash
npm run package-electron
```

The installer will be created in the `dist-electron` directory.

## 🔐 Security Features

- **Authentication**: Netlify Identity with JWT tokens
- **Authorization**: Role-based access control
- **Rate Limiting**: API endpoint protection
- **Input Validation**: Server-side validation with Zod
- **CORS**: Configured for secure cross-origin requests
- **CSP**: Content Security Policy headers

## 📊 Database Schema

### Tables
- `biography` - Personal information and bio
- `certificates` - Professional certifications
- `products` - Products and projects
- `services` - Service offerings
- `testimonials` - Client testimonials
- `contact_messages` - Contact form submissions

## 🎨 Customization

### Styling
- Modify `tailwind.config.js` for design system changes
- Update `src/index.css` for global styles
- Component styles use Tailwind utility classes

### Content
- Update personal information in the admin panel
- Customize sections by modifying page components
- Add new content types by extending the database schema

## 🔧 API Endpoints

### Public Endpoints
- `GET /api/biography` - Get biography information
- `GET /api/certificates` - List certificates
- `GET /api/products` - List products
- `GET /api/services` - List services
- `GET /api/testimonials` - List testimonials

### Admin Endpoints (Authentication Required)
- `POST/PUT /api/biography` - Update biography
- `POST/PUT/DELETE /api/certificates` - Manage certificates
- `POST/PUT/DELETE /api/products` - Manage products
- `POST/PUT/DELETE /api/services` - Manage services
- `POST/PUT/DELETE /api/testimonials` - Manage testimonials

## 🐛 Troubleshooting

### Common Issues

1. **Build Errors**
   - Ensure Node.js version is 18+
   - Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`

2. **Netlify Functions Not Working**
   - Check function logs in Netlify dashboard
   - Verify environment variables are set
   - Ensure functions are in the correct directory

3. **Electron App Won't Start**
   - Build the React app first: `npm run build`
   - Check Electron version compatibility

4. **Database Issues**
   - Verify D1 database is properly configured
   - Check database schema is up to date

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `docs/` folder
- Review the troubleshooting section above
