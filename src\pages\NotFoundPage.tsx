import React from 'react'
import { Link } from 'react-router-dom'
import { Home, ArrowRight } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Button from '@/components/ui/Button'

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 flex items-center justify-center px-4">
      <div className="text-center text-white max-w-md mx-auto">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-white/20 mb-4">404</h1>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('errors.notFound')}
          </h2>
          <p className="text-lg text-white/80 mb-8">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
          </p>
        </div>

        <div className="space-y-4">
          <Link to="/">
            <Button
              variant="primary"
              size="lg"
              className="bg-white text-primary-900 hover:bg-primary-50"
            >
              <Home size={20} className="ml-2" />
              العودة للرئيسية
            </Button>
          </Link>
          
          <button
            onClick={() => window.history.back()}
            className="block w-full text-white/80 hover:text-white transition-colors"
          >
            <ArrowRight size={16} className="inline ml-1" />
            العودة للصفحة السابقة
          </button>
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
