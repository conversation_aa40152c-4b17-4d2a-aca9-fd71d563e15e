import React from 'react'
import { Download, ExternalLink, Mail, Phone, MapPin } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import But<PERSON> from '@/components/ui/Button'
import Card from '@/components/ui/Card'

const AboutSection: React.FC = () => {
  const { t } = useTranslation()
  const { state } = useData()
  const biography = state.biography

  if (!biography) return null

  return (
    <section id="about" className="section bg-gradient-to-br from-secondary-50 to-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            {t('home.about.title')}
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            {t('home.about.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6 animate-slide-in-right">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 mb-4">
                {biography.full_name}
              </h3>
              <p className="text-xl text-primary-600 font-medium mb-6">
                {biography.title}
              </p>
            </div>

            {biography.description && (
              <div className="prose prose-lg text-secondary-700 leading-relaxed">
                <p>{biography.description}</p>
              </div>
            )}

            {/* Contact Info */}
            <div className="space-y-4">
              {biography.email && (
                <div className="flex items-center space-x-3 text-secondary-700">
                  <Mail size={20} className="text-primary-600" />
                  <a 
                    href={`mailto:${biography.email}`}
                    className="hover:text-primary-600 transition-colors"
                  >
                    {biography.email}
                  </a>
                </div>
              )}
              
              {biography.phone && (
                <div className="flex items-center space-x-3 text-secondary-700">
                  <Phone size={20} className="text-primary-600" />
                  <a 
                    href={`tel:${biography.phone}`}
                    className="hover:text-primary-600 transition-colors"
                  >
                    {biography.phone}
                  </a>
                </div>
              )}
              
              {biography.location && (
                <div className="flex items-center space-x-3 text-secondary-700">
                  <MapPin size={20} className="text-primary-600" />
                  <span>{biography.location}</span>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              {biography.pdf_url && (
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => window.open(biography.pdf_url, '_blank')}
                >
                  <Download size={20} className="ml-2" />
                  {t('home.about.downloadCv')}
                </Button>
              )}
              
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const contactSection = document.querySelector('#contact')
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                <Mail size={20} className="ml-2" />
                {t('home.hero.contact')}
              </Button>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4 pt-4">
              {biography.linkedin_url && (
                <a
                  href={biography.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 bg-primary-100 hover:bg-primary-600 text-primary-600 hover:text-white rounded-xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-1"
                >
                  <ExternalLink size={20} />
                </a>
              )}
              
              {biography.github_url && (
                <a
                  href={biography.github_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 bg-secondary-100 hover:bg-secondary-600 text-secondary-600 hover:text-white rounded-xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-1"
                >
                  <ExternalLink size={20} />
                </a>
              )}
              
              {biography.twitter_url && (
                <a
                  href={biography.twitter_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-12 h-12 bg-blue-100 hover:bg-blue-600 text-blue-600 hover:text-white rounded-xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-1"
                >
                  <ExternalLink size={20} />
                </a>
              )}
            </div>
          </div>

          {/* Image/Visual */}
          <div className="animate-slide-in-left">
            <Card hover gradient className="p-8">
              <div className="text-center">
                {biography.profile_image_url ? (
                  <div className="w-64 h-64 mx-auto rounded-2xl overflow-hidden mb-6 shadow-2xl">
                    <img 
                      src={biography.profile_image_url} 
                      alt={biography.full_name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-64 h-64 mx-auto rounded-2xl bg-gradient-to-br from-primary-600 to-primary-800 flex items-center justify-center text-white text-6xl font-bold mb-6 shadow-2xl">
                    {biography.full_name?.charAt(0) || 'س'}
                  </div>
                )}
                
                <h4 className="text-xl font-bold text-secondary-900 mb-2">
                  {biography.full_name}
                </h4>
                <p className="text-primary-600 font-medium">
                  {biography.title}
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection
