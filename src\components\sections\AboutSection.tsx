import React from 'react'
import { Download, ExternalLink, Mail, Phone, MapPin } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import But<PERSON> from '@/components/ui/Button'
import Card from '@/components/ui/Card'

const AboutSection: React.FC = () => {
  const { t } = useTranslation()
  const { state } = useData()
  const biography = state.biography

  if (!biography) return null

  return (
    <section id="about" className="section bg-gradient-to-br from-primary-50 via-white to-accent-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-200/30 to-accent-200/30 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-accent-200/30 to-primary-200/30 rounded-full blur-2xl"></div>
      </div>

      <div className="container relative z-10">
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-accent-600 to-primary-800 bg-clip-text text-transparent">
              {t('home.about.title')}
            </span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            {t('home.about.subtitle')}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-accent-600 mx-auto mt-6 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Enhanced Content */}
          <div className="space-y-8 animate-slide-in-right">
            <div className="text-center lg:text-right">
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="bg-gradient-to-r from-secondary-900 to-primary-700 bg-clip-text text-transparent">
                  {biography.full_name}
                </span>
              </h3>
              <p className="text-2xl font-semibold mb-6">
                <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
                  {biography.title}
                </span>
              </p>
            </div>

            {biography.description && (
              <div className="prose prose-lg text-secondary-700 leading-relaxed">
                <p>{biography.description}</p>
              </div>
            )}

            {/* Enhanced Contact Info */}
            <div className="space-y-6">
              {biography.email && (
                <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl border border-primary-200/50 hover:shadow-lg transition-all duration-300 group">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary-600 to-accent-600 rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                    <Mail size={20} />
                  </div>
                  <a
                    href={`mailto:${biography.email}`}
                    className="text-secondary-700 hover:text-primary-600 transition-colors font-medium text-lg"
                  >
                    {biography.email}
                  </a>
                </div>
              )}

              {biography.phone && (
                <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-accent-50 to-primary-50 rounded-xl border border-accent-200/50 hover:shadow-lg transition-all duration-300 group">
                  <div className="w-12 h-12 bg-gradient-to-r from-accent-600 to-primary-600 rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                    <Phone size={20} />
                  </div>
                  <a
                    href={`tel:${biography.phone}`}
                    className="text-secondary-700 hover:text-accent-600 transition-colors font-medium text-lg"
                  >
                    {biography.phone}
                  </a>
                </div>
              )}

              {biography.location && (
                <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-secondary-50 to-primary-50 rounded-xl border border-secondary-200/50 hover:shadow-lg transition-all duration-300 group">
                  <div className="w-12 h-12 bg-gradient-to-r from-secondary-600 to-primary-600 rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                    <MapPin size={20} />
                  </div>
                  <span className="text-secondary-700 font-medium text-lg">{biography.location}</span>
                </div>
              )}
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 pt-8">
              {biography.pdf_url && (
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => window.open(biography.pdf_url, '_blank')}
                  className="bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 shadow-xl hover:shadow-2xl hover:shadow-primary-500/25 transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
                >
                  <Download size={22} className="ml-2" />
                  {t('home.about.downloadCv')}
                </Button>
              )}

              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const contactSection = document.querySelector('#contact')
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
                className="border-2 border-gradient-to-r from-primary-600 to-accent-600 text-primary-600 hover:bg-gradient-to-r hover:from-primary-600 hover:to-accent-600 hover:text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
              >
                <Mail size={22} className="ml-2" />
                {t('home.hero.contact')}
              </Button>
            </div>

            {/* Enhanced Social Links */}
            <div className="flex space-x-6 pt-8 justify-center lg:justify-start">
              {biography.linkedin_url && (
                <a
                  href={biography.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-14 h-14 bg-gradient-to-r from-primary-100 to-accent-100 hover:from-primary-600 hover:to-accent-600 text-primary-600 hover:text-white rounded-2xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-2 hover:scale-110 shadow-lg hover:shadow-xl hover:shadow-primary-500/25"
                >
                  <ExternalLink size={24} />
                </a>
              )}

              {biography.github_url && (
                <a
                  href={biography.github_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-14 h-14 bg-gradient-to-r from-secondary-100 to-primary-100 hover:from-secondary-600 hover:to-primary-600 text-secondary-600 hover:text-white rounded-2xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-2 hover:scale-110 shadow-lg hover:shadow-xl hover:shadow-secondary-500/25"
                >
                  <ExternalLink size={24} />
                </a>
              )}

              {biography.twitter_url && (
                <a
                  href={biography.twitter_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-14 h-14 bg-gradient-to-r from-blue-100 to-accent-100 hover:from-blue-600 hover:to-accent-600 text-blue-600 hover:text-white rounded-2xl flex items-center justify-center transition-all duration-300 transform hover:-translate-y-2 hover:scale-110 shadow-lg hover:shadow-xl hover:shadow-blue-500/25"
                >
                  <ExternalLink size={24} />
                </a>
              )}
            </div>
          </div>

          {/* Enhanced Image/Visual */}
          <div className="animate-slide-in-left">
            <div className="relative">
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary-200/30 to-accent-200/30 rounded-3xl transform rotate-6 scale-105"></div>

              <Card hover className="relative bg-gradient-to-br from-white via-primary-50/50 to-accent-50/50 p-10 border-2 border-primary-200/50 shadow-2xl hover:shadow-primary-500/20">
                <div className="text-center">
                  <div className="relative mb-8">
                    {biography.profile_image_url ? (
                      <div className="w-72 h-72 mx-auto rounded-3xl overflow-hidden shadow-2xl border-4 border-white">
                        <img
                          src={biography.profile_image_url}
                          alt={biography.full_name}
                          className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                        />
                      </div>
                    ) : (
                      <div className="w-72 h-72 mx-auto rounded-3xl bg-gradient-to-br from-primary-600 via-accent-600 to-primary-800 flex items-center justify-center text-white text-8xl font-bold shadow-2xl border-4 border-white animate-gradient-xy">
                        {biography.full_name?.charAt(0) || 'س'}
                      </div>
                    )}

                    {/* Floating elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-accent-400 to-primary-400 rounded-full animate-bounce-gentle"></div>
                    <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-r from-primary-400 to-accent-400 rounded-full animate-bounce-gentle delay-500"></div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-2xl font-bold">
                      <span className="bg-gradient-to-r from-secondary-900 to-primary-700 bg-clip-text text-transparent">
                        {biography.full_name}
                      </span>
                    </h4>
                    <p className="text-lg font-semibold">
                      <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
                        {biography.title}
                      </span>
                    </p>

                    {/* Decorative line */}
                    <div className="w-16 h-1 bg-gradient-to-r from-primary-600 to-accent-600 mx-auto rounded-full"></div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection
