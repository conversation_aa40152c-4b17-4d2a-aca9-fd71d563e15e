// File Upload API endpoint
import { Handler, Context } from '@netlify/functions'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
      body: '',
    }
  }

  if (event.httpMethod !== 'POST') {
    return createErrorResponse(405, 'Method not allowed')
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 20, 15 * 60 * 1000) // 20 uploads per 15 minutes
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    // Require admin authentication
    const authResult = requireAdmin(context)
    if (!authResult.success) {
      return createErrorResponse(401, authResult.error || 'Authentication required')
    }

    return await handleFileUpload(event, rateLimit)
  } catch (error) {
    console.error('Upload API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function handleFileUpload(event: any, rateLimit: any) {
  try {
    // Parse multipart form data
    const contentType = event.headers['content-type'] || ''
    
    if (!contentType.includes('multipart/form-data')) {
      return createErrorResponse(400, 'Content-Type must be multipart/form-data')
    }

    // In a real implementation, you would:
    // 1. Parse the multipart form data
    // 2. Validate file type and size
    // 3. Upload to a storage service (Netlify Large Media, Cloudinary, etc.)
    // 4. Return the file URL

    // For this demo, we'll simulate file upload
    const body = event.body
    if (!body) {
      return createErrorResponse(400, 'No file data provided')
    }

    // Simulate file validation
    const maxSize = 5 * 1024 * 1024 // 5MB
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf',
      'text/plain'
    ]

    // In a real implementation, you would extract these from the multipart data
    const simulatedFile = {
      name: 'uploaded-file.jpg',
      type: 'image/jpeg',
      size: 1024 * 1024 // 1MB
    }

    // Validate file type
    if (!allowedTypes.includes(simulatedFile.type)) {
      return createErrorResponse(400, `File type ${simulatedFile.type} is not allowed`)
    }

    // Validate file size
    if (simulatedFile.size > maxSize) {
      return createErrorResponse(400, `File size exceeds maximum limit of ${maxSize / 1024 / 1024}MB`)
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const extension = simulatedFile.name.split('.').pop()
    const uniqueFilename = `${timestamp}-${randomString}.${extension}`

    // Simulate upload to storage service
    // In production, you would upload to:
    // - Netlify Large Media
    // - Cloudinary
    // - AWS S3
    // - Google Cloud Storage
    // - etc.

    const simulatedUploadUrl = `https://your-storage-service.com/uploads/${uniqueFilename}`

    // Log upload for monitoring
    console.log('File uploaded:', {
      originalName: simulatedFile.name,
      filename: uniqueFilename,
      type: simulatedFile.type,
      size: simulatedFile.size,
      url: simulatedUploadUrl
    })

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          url: simulatedUploadUrl,
          filename: uniqueFilename,
          originalName: simulatedFile.name,
          type: simulatedFile.type,
          size: simulatedFile.size
        },
        message: 'File uploaded successfully'
      })
    }
  } catch (error) {
    console.error('File upload error:', error)
    return createErrorResponse(500, 'Failed to upload file', error)
  }
}

// Helper function for real multipart parsing (would need additional dependencies)
function parseMultipartData(body: string, boundary: string) {
  // This is a simplified example
  // In production, use a proper multipart parser like 'busboy' or 'formidable'
  
  const parts = body.split(`--${boundary}`)
  const files: any[] = []
  const fields: Record<string, string> = {}

  for (const part of parts) {
    if (part.includes('Content-Disposition: form-data')) {
      const lines = part.split('\r\n')
      const dispositionLine = lines.find(line => line.includes('Content-Disposition'))
      
      if (dispositionLine?.includes('filename=')) {
        // This is a file
        const nameMatch = dispositionLine.match(/name="([^"]+)"/)
        const filenameMatch = dispositionLine.match(/filename="([^"]+)"/)
        const contentTypeLine = lines.find(line => line.includes('Content-Type:'))
        
        if (nameMatch && filenameMatch) {
          const contentStart = part.indexOf('\r\n\r\n') + 4
          const content = part.substring(contentStart)
          
          files.push({
            fieldName: nameMatch[1],
            filename: filenameMatch[1],
            contentType: contentTypeLine?.split(':')[1]?.trim() || 'application/octet-stream',
            content: content,
            size: content.length
          })
        }
      } else {
        // This is a regular field
        const nameMatch = dispositionLine?.match(/name="([^"]+)"/)
        if (nameMatch) {
          const contentStart = part.indexOf('\r\n\r\n') + 4
          const content = part.substring(contentStart).trim()
          fields[nameMatch[1]] = content
        }
      }
    }
  }

  return { files, fields }
}

// Helper function to generate secure filename
function generateSecureFilename(originalName: string): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = originalName.split('.').pop()?.toLowerCase()
  
  // Sanitize extension
  const allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'pdf', 'txt']
  const safeExtension = allowedExtensions.includes(extension || '') ? extension : 'bin'
  
  return `${timestamp}-${randomString}.${safeExtension}`
}

// Helper function to detect file type from content
function detectFileType(content: Buffer): string {
  // Check file signatures (magic numbers)
  const signatures = {
    'image/jpeg': [0xFF, 0xD8, 0xFF],
    'image/png': [0x89, 0x50, 0x4E, 0x47],
    'image/gif': [0x47, 0x49, 0x46],
    'image/webp': [0x52, 0x49, 0x46, 0x46],
    'application/pdf': [0x25, 0x50, 0x44, 0x46]
  }

  for (const [mimeType, signature] of Object.entries(signatures)) {
    if (signature.every((byte, index) => content[index] === byte)) {
      return mimeType
    }
  }

  return 'application/octet-stream'
}
