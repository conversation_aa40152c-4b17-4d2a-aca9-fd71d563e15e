import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '@/store/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import AdminLayout from '@/components/admin/AdminLayout'
import AdminDashboard from '@/components/admin/AdminDashboard'
import AdminLogin from '@/components/admin/AdminLogin'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

const AdminPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth()
  const { t } = useTranslation()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text={t('common.loading')} />
      </div>
    )
  }

  if (!isAuthenticated) {
    return <AdminLogin />
  }

  return (
    <AdminLayout>
      <Routes>
        <Route index element={<AdminDashboard />} />
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="biography" element={<div>Biography Management</div>} />
        <Route path="certificates" element={<div>Certificates Management</div>} />
        <Route path="products" element={<div>Products Management</div>} />
        <Route path="services" element={<div>Services Management</div>} />
        <Route path="testimonials" element={<div>Testimonials Management</div>} />
        <Route path="messages" element={<div>Messages Management</div>} />
        <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </AdminLayout>
  )
}

export default AdminPage
