// Biography API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, biographySchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 100, 15 * 60 * 1000)
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod

    switch (method) {
      case 'GET':
        return await getBiography(db, rateLimit)
      
      case 'POST':
      case 'PUT':
        return await updateBiography(event, context, db, rateLimit)
      
      case 'DELETE':
        return await deleteBiography(context, db, rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Biography API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getBiography(db: any, rateLimit: any) {
  try {
    const biography = await db
      .prepare('SELECT * FROM biography WHERE id = 1')
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)
    
    if (!biography) {
      return createSuccessResponse(null, 'No biography found')
    }

    // Parse JSON fields
    const result = {
      ...biography,
      created_at: biography.created_at,
      updated_at: biography.updated_at
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    }
  } catch (error) {
    console.error('Get biography error:', error)
    return createErrorResponse(500, 'Failed to fetch biography', error)
  }
}

async function updateBiography(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication for updates
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(biographySchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    // Check if biography exists
    const existing = await db
      .prepare('SELECT id FROM biography WHERE id = 1')
      .first()

    let result
    if (existing) {
      // Update existing biography
      result = await db
        .prepare(`
          UPDATE biography SET
            full_name = ?,
            title = ?,
            summary = ?,
            description = ?,
            email = ?,
            phone = ?,
            location = ?,
            website = ?,
            linkedin_url = ?,
            github_url = ?,
            twitter_url = ?,
            updated_at = ?
          WHERE id = 1
        `)
        .bind(
          data.full_name,
          data.title || null,
          data.summary || null,
          data.description || null,
          data.email || null,
          data.phone || null,
          data.location || null,
          data.website || null,
          data.linkedin_url || null,
          data.github_url || null,
          data.twitter_url || null,
          now
        )
        .run()
    } else {
      // Create new biography
      result = await db
        .prepare(`
          INSERT INTO biography (
            full_name, title, summary, description, email, phone, location,
            website, linkedin_url, github_url, twitter_url, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)
        .bind(
          data.full_name,
          data.title || null,
          data.summary || null,
          data.description || null,
          data.email || null,
          data.phone || null,
          data.location || null,
          data.website || null,
          data.linkedin_url || null,
          data.github_url || null,
          data.twitter_url || null,
          now,
          now
        )
        .run()
    }

    if (!result.success) {
      return createErrorResponse(500, 'Failed to save biography')
    }

    // Fetch updated biography
    const updated = await db
      .prepare('SELECT * FROM biography WHERE id = 1')
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: updated,
        message: existing ? 'Biography updated successfully' : 'Biography created successfully'
      })
    }
  } catch (error) {
    console.error('Update biography error:', error)
    return createErrorResponse(500, 'Failed to update biography', error)
  }
}

async function deleteBiography(context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM biography WHERE id = 1')
      .run()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Biography deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete biography error:', error)
    return createErrorResponse(500, 'Failed to delete biography', error)
  }
}
