import React, { createContext, useContext, useReducer } from 'react'
import { Biography, Certificate, Product, Service, Testimonial } from '@/types'

interface DataState {
  biography: Biography | null
  certificates: Certificate[]
  products: Product[]
  services: Service[]
  testimonials: Testimonial[]
  loading: {
    biography: boolean
    certificates: boolean
    products: boolean
    services: boolean
    testimonials: boolean
  }
  error: {
    biography: string | null
    certificates: string | null
    products: string | null
    services: string | null
    testimonials: string | null
  }
}

type DataAction =
  | { type: 'SET_LOADING'; payload: { key: keyof DataState['loading']; loading: boolean } }
  | { type: 'SET_ERROR'; payload: { key: keyof DataState['error']; error: string | null } }
  | { type: 'SET_BIOGRAPHY'; payload: Biography | null }
  | { type: 'SET_CERTIFICATES'; payload: Certificate[] }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'SET_SERVICES'; payload: Service[] }
  | { type: 'SET_TESTIMONIALS'; payload: Testimonial[] }
  | { type: 'ADD_CERTIFICATE'; payload: Certificate }
  | { type: 'UPDATE_CERTIFICATE'; payload: Certificate }
  | { type: 'DELETE_CERTIFICATE'; payload: number }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: number }
  | { type: 'ADD_SERVICE'; payload: Service }
  | { type: 'UPDATE_SERVICE'; payload: Service }
  | { type: 'DELETE_SERVICE'; payload: number }
  | { type: 'ADD_TESTIMONIAL'; payload: Testimonial }
  | { type: 'UPDATE_TESTIMONIAL'; payload: Testimonial }
  | { type: 'DELETE_TESTIMONIAL'; payload: number }

const initialState: DataState = {
  biography: null,
  certificates: [],
  products: [],
  services: [],
  testimonials: [],
  loading: {
    biography: false,
    certificates: false,
    products: false,
    services: false,
    testimonials: false,
  },
  error: {
    biography: null,
    certificates: null,
    products: null,
    services: null,
    testimonials: null,
  },
}

function dataReducer(state: DataState, action: DataAction): DataState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.loading,
        },
      }
    
    case 'SET_ERROR':
      return {
        ...state,
        error: {
          ...state.error,
          [action.payload.key]: action.payload.error,
        },
      }
    
    case 'SET_BIOGRAPHY':
      return { ...state, biography: action.payload }
    
    case 'SET_CERTIFICATES':
      return { ...state, certificates: action.payload }
    
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload }
    
    case 'SET_SERVICES':
      return { ...state, services: action.payload }
    
    case 'SET_TESTIMONIALS':
      return { ...state, testimonials: action.payload }
    
    case 'ADD_CERTIFICATE':
      return {
        ...state,
        certificates: [...state.certificates, action.payload],
      }
    
    case 'UPDATE_CERTIFICATE':
      return {
        ...state,
        certificates: state.certificates.map(cert =>
          cert.id === action.payload.id ? action.payload : cert
        ),
      }
    
    case 'DELETE_CERTIFICATE':
      return {
        ...state,
        certificates: state.certificates.filter(cert => cert.id !== action.payload),
      }
    
    case 'ADD_PRODUCT':
      return {
        ...state,
        products: [...state.products, action.payload],
      }
    
    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? action.payload : product
        ),
      }
    
    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      }
    
    case 'ADD_SERVICE':
      return {
        ...state,
        services: [...state.services, action.payload],
      }
    
    case 'UPDATE_SERVICE':
      return {
        ...state,
        services: state.services.map(service =>
          service.id === action.payload.id ? action.payload : service
        ),
      }
    
    case 'DELETE_SERVICE':
      return {
        ...state,
        services: state.services.filter(service => service.id !== action.payload),
      }
    
    case 'ADD_TESTIMONIAL':
      return {
        ...state,
        testimonials: [...state.testimonials, action.payload],
      }
    
    case 'UPDATE_TESTIMONIAL':
      return {
        ...state,
        testimonials: state.testimonials.map(testimonial =>
          testimonial.id === action.payload.id ? action.payload : testimonial
        ),
      }
    
    case 'DELETE_TESTIMONIAL':
      return {
        ...state,
        testimonials: state.testimonials.filter(testimonial => testimonial.id !== action.payload),
      }
    
    default:
      return state
  }
}

interface DataContextType {
  state: DataState
  dispatch: React.Dispatch<DataAction>
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider')
  }
  return context
}

interface DataProviderProps {
  children: React.ReactNode
}

export function DataProvider({ children }: DataProviderProps) {
  const [state, dispatch] = useReducer(dataReducer, initialState)

  const value: DataContextType = {
    state,
    dispatch,
  }

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>
}
