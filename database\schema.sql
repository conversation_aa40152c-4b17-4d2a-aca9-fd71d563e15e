-- Cinmana Personal Website Database Schema
-- Compatible with Netlify D1 (SQLite)

-- Biography table
CREATE TABLE IF NOT EXISTS biography (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name TEXT NOT NULL,
    title TEXT,
    summary TEXT,
    description TEXT,
    email TEXT,
    phone TEXT,
    location TEXT,
    website TEXT,
    linkedin_url TEXT,
    github_url TEXT,
    twitter_url TEXT,
    pdf_url TEXT,
    profile_image_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Certificates table
CREATE TABLE IF NOT EXISTS certificates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    issuer TEXT NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE,
    credential_id TEXT,
    credential_url TEXT,
    file_url TEXT,
    description TEXT,
    skills TEXT, -- JSON array of skills
    is_featured BOOLEAN DEFAULT FALSE,
    display_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    short_description TEXT,
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    image_url TEXT,
    gallery_urls TEXT, -- JSON array of image URLs
    purchase_link TEXT,
    demo_link TEXT,
    github_link TEXT,
    category TEXT,
    tags TEXT, -- JSON array of tags
    features TEXT, -- JSON array of features
    is_featured BOOLEAN DEFAULT FALSE,
    is_available BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    short_description TEXT,
    price_range TEXT,
    price_from DECIMAL(10,2),
    price_to DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    icon_name TEXT,
    image_url TEXT,
    category TEXT,
    duration TEXT,
    deliverables TEXT, -- JSON array of deliverables
    features TEXT, -- JSON array of features
    is_featured BOOLEAN DEFAULT FALSE,
    is_available BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Testimonials table
CREATE TABLE IF NOT EXISTS testimonials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author TEXT NOT NULL,
    role TEXT,
    company TEXT,
    avatar_url TEXT,
    quote TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    project_name TEXT,
    service_category TEXT,
    date DATE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Contact messages table (for contact form submissions)
CREATE TABLE IF NOT EXISTS contact_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT,
    message TEXT NOT NULL,
    phone TEXT,
    company TEXT,
    service_interest TEXT,
    status TEXT DEFAULT 'new', -- new, read, replied, archived
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_certificates_issue_date ON certificates(issue_date DESC);
CREATE INDEX IF NOT EXISTS idx_certificates_featured ON certificates(is_featured, display_order);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured, display_order);
CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_services_featured ON services(is_featured, display_order);
CREATE INDEX IF NOT EXISTS idx_services_available ON services(is_available);
CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_testimonials_featured ON testimonials(is_featured, display_order);
CREATE INDEX IF NOT EXISTS idx_testimonials_approved ON testimonials(is_approved);
CREATE INDEX IF NOT EXISTS idx_testimonials_date ON testimonials(date DESC);
CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON contact_messages(status);
CREATE INDEX IF NOT EXISTS idx_contact_messages_created ON contact_messages(created_at DESC);

-- Insert sample data for development
INSERT OR IGNORE INTO biography (id, full_name, title, summary, email, phone, location) VALUES 
(1, 'Your Name', 'Full Stack Developer & Designer', 'Passionate developer with expertise in modern web technologies and user experience design.', '<EMAIL>', '+****************', 'Your City, Country');

INSERT OR IGNORE INTO certificates (title, issuer, issue_date, description) VALUES 
('AWS Certified Solutions Architect', 'Amazon Web Services', '2023-01-15', 'Professional certification demonstrating expertise in designing distributed systems on AWS.'),
('React Developer Certification', 'Meta', '2023-03-20', 'Advanced certification in React development and modern frontend practices.'),
('Google Cloud Professional', 'Google Cloud', '2023-06-10', 'Professional certification in Google Cloud Platform services and architecture.');

INSERT OR IGNORE INTO products (name, description, price, category, is_featured) VALUES 
('Personal Portfolio Template', 'Modern, responsive portfolio template built with React and Tailwind CSS.', 49.99, 'Templates', TRUE),
('E-commerce Starter Kit', 'Complete e-commerce solution with payment integration and admin dashboard.', 199.99, 'Starter Kits', TRUE),
('UI Component Library', 'Comprehensive library of reusable React components with TypeScript support.', 79.99, 'Components', FALSE);

INSERT OR IGNORE INTO services (name, description, price_range, icon_name, is_featured) VALUES 
('Web Development', 'Custom web applications built with modern technologies and best practices.', '$2,000 - $10,000', 'code', TRUE),
('UI/UX Design', 'User-centered design solutions that enhance user experience and drive engagement.', '$1,500 - $5,000', 'palette', TRUE),
('Consulting', 'Technical consulting and architecture review for your existing projects.', '$150 - $300/hour', 'users', FALSE);

INSERT OR IGNORE INTO testimonials (author, role, company, quote, rating, is_featured) VALUES 
('John Smith', 'CTO', 'Tech Startup Inc.', 'Exceptional work quality and attention to detail. Delivered our project on time and exceeded expectations.', 5, TRUE),
('Sarah Johnson', 'Product Manager', 'Digital Agency', 'Professional, reliable, and highly skilled. Would definitely work with them again.', 5, TRUE),
('Mike Chen', 'Founder', 'E-commerce Co.', 'Transformed our vision into a beautiful, functional website. Great communication throughout the project.', 5, FALSE);
