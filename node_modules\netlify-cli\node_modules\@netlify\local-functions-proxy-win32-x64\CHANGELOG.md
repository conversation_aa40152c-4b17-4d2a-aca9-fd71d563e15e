# Changelog

### [1.1.1](https://www.github.com/netlify/local-functions-proxy/compare/local-functions-proxy-win32-x64-v1.1.0...local-functions-proxy-win32-x64-v1.1.1) (2021-08-12)


### Bug Fixes

* forward stdout to stderr ([#54](https://www.github.com/netlify/local-functions-proxy/issues/54)) ([0d965f7](https://www.github.com/netlify/local-functions-proxy/commit/0d965f7951bd31f656c883c3d83987974762785a))

## [1.1.0](https://www.github.com/netlify/local-functions-proxy/compare/local-functions-proxy-win32-x64-v1.0.0...local-functions-proxy-win32-x64-v1.1.0) (2021-07-29)


### Features

* add headers and missing params to request ([#40](https://www.github.com/netlify/local-functions-proxy/issues/40)) ([7f1950b](https://www.github.com/netlify/local-functions-proxy/commit/7f1950b3fcb8a52cbc00368c16bd8a82819a55a8))

## 1.0.0 (2021-07-16)


### Features

* remove comment ([#11](https://www.github.com/netlify/local-functions-proxy/issues/11)) ([32c7b11](https://www.github.com/netlify/local-functions-proxy/commit/32c7b113735e1a60cc0eaebc6043125b340640c7))
* update packages ([ffccc55](https://www.github.com/netlify/local-functions-proxy/commit/ffccc555809f30eb151b3db5e719b11190d57bb9))
