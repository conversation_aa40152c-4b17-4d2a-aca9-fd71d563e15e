// Input validation utilities
import { z } from 'zod'

// Biography validation schema
export const biographySchema = z.object({
  full_name: z.string().min(1, 'Full name is required').max(100),
  title: z.string().max(200).optional(),
  summary: z.string().max(500).optional(),
  description: z.string().max(2000).optional(),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().max(20).optional(),
  location: z.string().max(100).optional(),
  website: z.string().url('Invalid URL format').optional(),
  linkedin_url: z.string().url('Invalid URL format').optional(),
  github_url: z.string().url('Invalid URL format').optional(),
  twitter_url: z.string().url('Invalid URL format').optional(),
})

// Certificate validation schema
export const certificateSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200),
  issuer: z.string().min(1, 'Issuer is required').max(200),
  issue_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  expiry_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
  credential_id: z.string().max(100).optional(),
  credential_url: z.string().url('Invalid URL format').optional(),
  description: z.string().max(1000).optional(),
  skills: z.array(z.string().max(50)).max(20).optional(),
  is_featured: z.boolean().optional(),
})

// Product validation schema
export const productSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200),
  description: z.string().max(2000).optional(),
  short_description: z.string().max(300).optional(),
  price: z.number().min(0, 'Price must be positive').max(999999.99).optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  purchase_link: z.string().url('Invalid URL format').optional(),
  demo_link: z.string().url('Invalid URL format').optional(),
  github_link: z.string().url('Invalid URL format').optional(),
  category: z.string().max(100).optional(),
  tags: z.array(z.string().max(50)).max(20).optional(),
  features: z.array(z.string().max(200)).max(50).optional(),
  is_featured: z.boolean().optional(),
  is_available: z.boolean().optional(),
})

// Service validation schema
export const serviceSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200),
  description: z.string().max(2000).optional(),
  short_description: z.string().max(300).optional(),
  price_range: z.string().max(100).optional(),
  price_from: z.number().min(0, 'Price must be positive').max(999999.99).optional(),
  price_to: z.number().min(0, 'Price must be positive').max(999999.99).optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  icon_name: z.string().max(50).optional(),
  category: z.string().max(100).optional(),
  duration: z.string().max(100).optional(),
  deliverables: z.array(z.string().max(200)).max(50).optional(),
  features: z.array(z.string().max(200)).max(50).optional(),
  is_featured: z.boolean().optional(),
  is_available: z.boolean().optional(),
})

// Testimonial validation schema
export const testimonialSchema = z.object({
  author: z.string().min(1, 'Author is required').max(100),
  role: z.string().max(100).optional(),
  company: z.string().max(100).optional(),
  quote: z.string().min(1, 'Quote is required').max(1000),
  rating: z.number().int().min(1, 'Rating must be 1-5').max(5).optional(),
  project_name: z.string().max(200).optional(),
  service_category: z.string().max(100).optional(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
  is_featured: z.boolean().optional(),
})

// Contact message validation schema
export const contactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email format'),
  subject: z.string().max(200).optional(),
  message: z.string().min(1, 'Message is required').max(2000),
  phone: z.string().max(20).optional(),
  company: z.string().max(100).optional(),
  service_interest: z.string().max(100).optional(),
})

// File upload validation
export const fileUploadSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  contentType: z.string().min(1, 'Content type is required'),
  size: z.number().max(5 * 1024 * 1024, 'File size must be less than 5MB'),
})

// Query parameters validation
export const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be positive').optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be 1-100').optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  featured: z.enum(['true', 'false']).transform(v => v === 'true').optional(),
})

// Validation helper function
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const result = schema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    return { success: false, errors: ['Validation failed'] }
  }
}

// Sanitization helpers
export function sanitizeString(str: string): string {
  return str.trim().replace(/[<>]/g, '')
}

export function sanitizeHtml(html: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}

export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url)
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol')
    }
    return parsed.toString()
  } catch {
    throw new Error('Invalid URL')
  }
}
