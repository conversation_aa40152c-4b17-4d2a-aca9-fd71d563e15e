import React from 'react'
import { Code, Palette, Users } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'

const ServicesSection: React.FC = () => {
  const { t } = useTranslation()

  const services = [
    {
      id: 1,
      name: 'تطوير المواقع',
      description: 'تطوير مواقع ويب حديثة ومتجاوبة باستخدام أحدث التقنيات',
      icon: Code,
      price_range: '$2,000 - $10,000'
    },
    {
      id: 2,
      name: 'تصميم واجهات المستخدم',
      description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام',
      icon: Palette,
      price_range: '$1,500 - $5,000'
    },
    {
      id: 3,
      name: 'الاستشارات التقنية',
      description: 'استشارات تقنية ومراجعة الأنظمة والحلول',
      icon: Users,
      price_range: '$150 - $300/ساعة'
    }
  ]

  return (
    <section id="services" className="section bg-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            {t('home.services.title')}
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            {t('home.services.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <Card key={service.id} hover gradient className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-primary-800 rounded-2xl flex items-center justify-center text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <service.icon size={32} />
              </div>
              
              <h3 className="text-xl font-bold text-secondary-900 mb-4 group-hover:text-primary-600 transition-colors">
                {service.name}
              </h3>
              
              <p className="text-secondary-700 mb-6 leading-relaxed">
                {service.description}
              </p>
              
              <div className="text-primary-600 font-bold text-lg">
                {t('home.services.startingFrom')} {service.price_range}
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ServicesSection
