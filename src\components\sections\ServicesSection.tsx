import React from 'react'
import { Code, Palette, Users } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'

const ServicesSection: React.FC = () => {
  const { t } = useTranslation()

  const services = [
    {
      id: 1,
      name: 'تطوير المواقع',
      description: 'تطوير مواقع ويب حديثة ومتجاوبة باستخدام أحدث التقنيات',
      icon: Code,
      price_range: '$2,000 - $10,000'
    },
    {
      id: 2,
      name: 'تصميم واجهات المستخدم',
      description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام',
      icon: Palette,
      price_range: '$1,500 - $5,000'
    },
    {
      id: 3,
      name: 'الاستشارات التقنية',
      description: 'استشارات تقنية ومراجعة الأنظمة والحلول',
      icon: Users,
      price_range: '$150 - $300/ساعة'
    }
  ]

  return (
    <section id="services" className="section bg-gradient-to-br from-secondary-50 via-white to-primary-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-primary-200/20 to-accent-200/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-accent-200/20 to-primary-200/20 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="container relative z-10">
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-accent-600 to-primary-800 bg-clip-text text-transparent">
              {t('home.services.title')}
            </span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            {t('home.services.subtitle')}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-accent-600 mx-auto mt-6 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {services.map((service, index) => (
            <Card
              key={service.id}
              hover
              className={`text-center group bg-gradient-to-br from-white via-primary-50/30 to-accent-50/30 border-2 border-primary-200/50 shadow-xl hover:shadow-2xl hover:shadow-primary-500/20 transform hover:-translate-y-3 transition-all duration-500 animate-fade-in-up relative overflow-hidden`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100/50 to-accent-100/50 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>

              <div className="relative z-10 space-y-6">
                {/* Enhanced Icon */}
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-600 via-accent-600 to-primary-800 rounded-3xl flex items-center justify-center text-white mx-auto group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl">
                    <service.icon size={36} />
                  </div>
                  {/* Floating particles */}
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-accent-400 rounded-full animate-bounce-gentle opacity-80"></div>
                  <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-primary-400 rounded-full animate-bounce-gentle delay-500 opacity-80"></div>
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold mb-4 group-hover:text-primary-600 transition-colors">
                  <span className="bg-gradient-to-r from-secondary-900 to-primary-700 bg-clip-text text-transparent group-hover:from-primary-600 group-hover:to-accent-600 transition-all duration-300">
                    {service.name}
                  </span>
                </h3>

                {/* Description */}
                <p className="text-secondary-700 leading-relaxed text-lg">
                  {service.description}
                </p>

                {/* Price with enhanced styling */}
                <div className="pt-4">
                  <div className="inline-block px-6 py-3 bg-gradient-to-r from-primary-600 to-accent-600 text-white rounded-2xl font-bold text-lg shadow-lg group-hover:shadow-xl group-hover:scale-105 transition-all duration-300">
                    {t('home.services.startingFrom')} {service.price_range}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ServicesSection
