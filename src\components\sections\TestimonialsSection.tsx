import React from 'react'
import { Star, Quote } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'

const TestimonialsSection: React.FC = () => {
  const { t } = useTranslation()

  const testimonials = [
    {
      id: 1,
      author: 'أحمد محمد',
      role: 'مدير تقني',
      company: 'شركة التقنية المتقدمة',
      quote: 'عمل استثنائي وجودة عالية في التنفيذ. تم تسليم المشروع في الوقت المحدد وتجاوز التوقعات.',
      rating: 5
    },
    {
      id: 2,
      author: 'سارة أحمد',
      role: 'مديرة المنتج',
      company: 'وكالة التسويق الرقمي',
      quote: 'محترف وموثوق ومهارات عالية. بالتأكيد سأعمل معه مرة أخرى في المشاريع القادمة.',
      rating: 5
    },
    {
      id: 3,
      author: 'محمد علي',
      role: 'مؤسس',
      company: 'شركة التجارة الإلكترونية',
      quote: 'حول رؤيتنا إلى موقع ويب جميل وعملي. تواصل ممتاز طوال فترة المشروع.',
      rating: 5
    }
  ]

  return (
    <section id="testimonials" className="section bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            {t('home.testimonials.title')}
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            {t('home.testimonials.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} hover className="relative">
              <Quote size={32} className="text-primary-200 mb-4" />
              
              <p className="text-secondary-700 mb-6 leading-relaxed italic">
                "{testimonial.quote}"
              </p>
              
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    size={16}
                    className={`${
                      i < testimonial.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-secondary-300'
                    }`}
                  />
                ))}
              </div>
              
              <div>
                <h4 className="font-bold text-secondary-900">
                  {testimonial.author}
                </h4>
                <p className="text-primary-600 font-medium">
                  {testimonial.role}
                </p>
                <p className="text-secondary-500 text-sm">
                  {testimonial.company}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection
