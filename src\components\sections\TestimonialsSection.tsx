import React from 'react'
import { Star, Quote } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'

const TestimonialsSection: React.FC = () => {
  const { t } = useTranslation()

  const testimonials = [
    {
      id: 1,
      author: 'أحمد محمد',
      role: 'مدير تقني',
      company: 'شركة التقنية المتقدمة',
      quote: 'عمل استثنائي وجودة عالية في التنفيذ. تم تسليم المشروع في الوقت المحدد وتجاوز التوقعات.',
      rating: 5
    },
    {
      id: 2,
      author: 'سارة أحمد',
      role: 'مديرة المنتج',
      company: 'وكالة التسويق الرقمي',
      quote: 'محترف وموثوق ومهارات عالية. بالتأكيد سأعمل معه مرة أخرى في المشاريع القادمة.',
      rating: 5
    },
    {
      id: 3,
      author: 'محمد علي',
      role: 'مؤسس',
      company: 'شركة التجارة الإلكترونية',
      quote: 'حول رؤيتنا إلى موقع ويب جميل وعملي. تواصل ممتاز طوال فترة المشروع.',
      rating: 5
    }
  ]

  return (
    <section id="testimonials" className="section bg-gradient-to-br from-accent-50 via-primary-50 to-secondary-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-40 left-20 w-72 h-72 bg-gradient-to-r from-accent-200/20 to-primary-200/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-40 right-20 w-96 h-96 bg-gradient-to-r from-primary-200/20 to-accent-200/20 rounded-full blur-3xl animate-float delay-1500"></div>
      </div>

      <div className="container relative z-10">
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-accent-600 via-primary-600 to-accent-800 bg-clip-text text-transparent">
              {t('home.testimonials.title')}
            </span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            {t('home.testimonials.subtitle')}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-accent-600 to-primary-600 mx-auto mt-6 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              hover
              className={`relative bg-gradient-to-br from-white via-accent-50/30 to-primary-50/30 border-2 border-accent-200/50 shadow-xl hover:shadow-2xl hover:shadow-accent-500/20 transform hover:-translate-y-3 transition-all duration-500 animate-fade-in-up overflow-hidden`}
              style={{ animationDelay: `${index * 0.15}s` }}
            >
              {/* Background decoration */}
              <div className="absolute top-0 left-0 w-24 h-24 bg-gradient-to-br from-accent-100/50 to-primary-100/50 rounded-full blur-xl transform -translate-x-12 -translate-y-12"></div>

              <div className="relative z-10 space-y-6">
                {/* Enhanced Quote Icon */}
                <div className="relative">
                  <Quote size={40} className="text-accent-300 opacity-60" />
                  <div className="absolute -top-2 -right-2 w-3 h-3 bg-accent-400 rounded-full animate-pulse"></div>
                </div>

                {/* Quote Text */}
                <blockquote className="text-secondary-700 text-lg leading-relaxed italic font-medium">
                  "{testimonial.quote}"
                </blockquote>

                {/* Enhanced Rating */}
                <div className="flex items-center justify-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      size={20}
                      className={`transition-all duration-300 ${
                        i < testimonial.rating
                          ? 'text-yellow-400 fill-current transform scale-110'
                          : 'text-secondary-300'
                      }`}
                    />
                  ))}
                </div>

                {/* Enhanced Author Info */}
                <div className="text-center pt-4 border-t border-accent-200/50">
                  <h4 className="font-bold text-xl mb-2">
                    <span className="bg-gradient-to-r from-secondary-900 to-accent-700 bg-clip-text text-transparent">
                      {testimonial.author}
                    </span>
                  </h4>
                  <p className="font-semibold text-lg mb-1">
                    <span className="bg-gradient-to-r from-accent-600 to-primary-600 bg-clip-text text-transparent">
                      {testimonial.role}
                    </span>
                  </p>
                  <p className="text-secondary-500 font-medium">
                    {testimonial.company}
                  </p>

                  {/* Decorative line */}
                  <div className="w-12 h-1 bg-gradient-to-r from-accent-600 to-primary-600 mx-auto mt-3 rounded-full"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection
