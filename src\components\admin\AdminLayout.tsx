import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  LayoutDashboard, 
  User, 
  Award, 
  Package, 
  Briefcase, 
  MessageSquare, 
  Mail,
  LogOut,
  Home
} from 'lucide-react'
import { useAuth } from '@/store/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import Button from '@/components/ui/Button'

interface AdminLayoutProps {
  children: React.ReactNode
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { logout, user } = useAuth()
  const { t } = useTranslation()
  const location = useLocation()

  const menuItems = [
    { icon: LayoutDashboard, label: t('admin.dashboard'), path: '/admin/dashboard' },
    { icon: User, label: t('admin.biography'), path: '/admin/biography' },
    { icon: Award, label: t('admin.certificates'), path: '/admin/certificates' },
    { icon: Package, label: t('admin.products'), path: '/admin/products' },
    { icon: Briefcase, label: t('admin.services'), path: '/admin/services' },
    { icon: MessageSquare, label: t('admin.testimonials'), path: '/admin/testimonials' },
    { icon: Mail, label: t('admin.messages'), path: '/admin/messages' },
  ]

  return (
    <div className="min-h-screen bg-secondary-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg border-l border-secondary-200">
        <div className="p-6 border-b border-secondary-200">
          <Link to="/" className="flex items-center space-x-3 text-xl font-bold text-primary-600">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-800 rounded-lg flex items-center justify-center text-white text-sm font-bold">
              س
            </div>
            <span>سينمانا</span>
          </Link>
        </div>

        <nav className="p-4 space-y-2">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors ${
                location.pathname === item.path
                  ? 'bg-primary-100 text-primary-700 border border-primary-200'
                  : 'text-secondary-700 hover:bg-secondary-100'
              }`}
            >
              <item.icon size={20} />
              <span className="font-medium">{item.label}</span>
            </Link>
          ))}
        </nav>

        <div className="absolute bottom-0 left-0 right-0 w-64 p-4 border-t border-secondary-200 bg-white">
          <div className="space-y-3">
            <div className="flex items-center space-x-3 px-4 py-2">
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <User size={16} className="text-primary-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-secondary-900 truncate">
                  {user?.email}
                </p>
                <p className="text-xs text-secondary-500">مدير</p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Link to="/" className="flex-1">
                <Button variant="outline" size="sm" className="w-full">
                  <Home size={16} className="ml-1" />
                  الموقع
                </Button>
              </Link>
              <Button variant="ghost" size="sm" onClick={logout}>
                <LogOut size={16} />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow-sm border-b border-secondary-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-secondary-900">
              {t('admin.title')}
            </h1>
            <div className="text-sm text-secondary-500">
              آخر تحديث: {new Date().toLocaleDateString('ar-SA')}
            </div>
          </div>
        </header>

        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}

export default AdminLayout
