import React from 'react'
import { Package, ExternalLink, Github } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const ProductsSection: React.FC = () => {
  const { t } = useTranslation()

  // Mock data for demonstration
  const products = [
    {
      id: 1,
      name: 'قالب موقع شخصي',
      description: 'قالب موقع شخصي حديث ومتجاوب مبني بـ React و Tailwind CSS',
      price: 49.99,
      image_url: '/placeholder-product.jpg',
      demo_link: '#',
      github_link: '#'
    },
    {
      id: 2,
      name: 'نظام إدارة المحتوى',
      description: 'نظام إدارة محتوى متكامل مع لوحة تحكم متقدمة',
      price: 199.99,
      image_url: '/placeholder-product.jpg',
      demo_link: '#',
      github_link: '#'
    }
  ]

  return (
    <section id="products" className="section bg-gradient-to-br from-secondary-50 to-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            {t('home.products.title')}
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            {t('home.products.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product) => (
            <Card key={product.id} hover className="group overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl mb-4 flex items-center justify-center">
                <Package size={48} className="text-primary-600" />
              </div>
              
              <h3 className="text-xl font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors">
                {product.name}
              </h3>
              
              <p className="text-secondary-700 mb-4 leading-relaxed">
                {product.description}
              </p>
              
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold text-primary-600">
                  ${product.price}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="primary" size="sm" className="flex-1">
                  <ExternalLink size={16} className="ml-1" />
                  {t('home.products.demo')}
                </Button>
                <Button variant="outline" size="sm">
                  <Github size={16} />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProductsSection
