import React, { useEffect, useState } from 'react'
import { ChevronDown, Download, Mail } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import { api } from '@/api/client'
import But<PERSON> from '@/components/ui/Button'

const HeroSection: React.FC = () => {
  const { t } = useTranslation()
  const { state, dispatch } = useData()
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const loadBiography = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'biography', loading: true } })
        const response = await api.biography.get()
        if (response.success && response.data) {
          dispatch({ type: 'SET_BIOGRAPHY', payload: response.data })
        }
      } catch (error) {
        console.error('Failed to load biography:', error)
        dispatch({ type: 'SET_ERROR', payload: { key: 'biography', error: 'Failed to load biography' } })
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'biography', loading: false } })
        setIsLoaded(true)
      }
    }

    loadBiography()
  }, [dispatch])

  const handleScrollToAbout = () => {
    const aboutSection = document.querySelector('#about')
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleContactClick = () => {
    const contactSection = document.querySelector('#contact')
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const biography = state.biography

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-600/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-600/20 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl animate-pulse delay-500" />
        </div>
        
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Profile Image */}
          <div className="mb-8 animate-fade-in-down">
            <div className="w-32 h-32 md:w-40 md:h-40 mx-auto rounded-full bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center overflow-hidden">
              {biography?.profile_image_url ? (
                <img 
                  src={biography.profile_image_url} 
                  alt={biography.full_name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-4xl md:text-5xl font-bold">
                  {biography?.full_name?.charAt(0) || 'س'}
                </div>
              )}
            </div>
          </div>

          {/* Text Content */}
          <div className="space-y-6 animate-fade-in-up">
            <div>
              <p className="text-primary-200 text-lg md:text-xl font-medium mb-2">
                {t('home.hero.title')}
              </p>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-4">
                {biography?.full_name || 'سينمانا'}
              </h1>
              <h2 className="text-xl md:text-2xl lg:text-3xl text-primary-200 font-medium">
                {biography?.title || t('home.hero.subtitle')}
              </h2>
            </div>

            <p className="text-lg md:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              {biography?.summary || t('home.hero.description')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
              <Button
                variant="primary"
                size="lg"
                onClick={handleScrollToAbout}
                className="bg-white text-primary-900 hover:bg-primary-50 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
              >
                {t('home.hero.cta')}
                <ChevronDown size={20} className="mr-2" />
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                onClick={handleContactClick}
                className="border-white text-white hover:bg-white hover:text-primary-900 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
              >
                <Mail size={20} className="ml-2" />
                {t('home.hero.contact')}
              </Button>
            </div>

            {/* CV Download */}
            {biography?.pdf_url && (
              <div className="pt-6">
                <a
                  href={biography.pdf_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-200 hover:text-white transition-colors"
                >
                  <Download size={18} className="ml-2" />
                  {t('home.about.downloadCv')}
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <button
            onClick={handleScrollToAbout}
            className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center"
          >
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse" />
          </button>
        </div>
      </div>

      {/* Loading State */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-primary-900 flex items-center justify-center z-20">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-white/20 border-t-white rounded-full animate-spin mx-auto mb-4" />
            <p className="text-white text-lg">{t('common.loading')}</p>
          </div>
        </div>
      )}
    </section>
  )
}

export default HeroSection
