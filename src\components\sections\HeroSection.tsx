import React, { useEffect, useState } from 'react'
import { ChevronDown, Download, Mail } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import { api } from '@/api/client'
import But<PERSON> from '@/components/ui/Button'

const HeroSection: React.FC = () => {
  const { t } = useTranslation()
  const { state, dispatch } = useData()
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const loadBiography = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'biography', loading: true } })
        const response = await api.biography.get()
        if (response.success && response.data) {
          dispatch({ type: 'SET_BIOGRAPHY', payload: response.data })
        }
      } catch (error) {
        console.error('Failed to load biography:', error)
        dispatch({ type: 'SET_ERROR', payload: { key: 'biography', error: 'Failed to load biography' } })
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'biography', loading: false } })
        setIsLoaded(true)
      }
    }

    loadBiography()
  }, [dispatch])

  const handleScrollToAbout = () => {
    const aboutSection = document.querySelector('#about')
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleContactClick = () => {
    const contactSection = document.querySelector('#contact')
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const biography = state.biography

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-900 via-accent-900 to-secondary-900">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-primary-600/30 to-accent-600/30 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-accent-600/20 to-primary-600/20 rounded-full blur-3xl animate-float delay-1000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-primary-500/15 to-accent-500/15 rounded-full blur-3xl animate-float delay-500" />
          <div className="absolute top-3/4 left-1/3 w-64 h-64 bg-gradient-to-r from-accent-400/25 to-primary-400/25 rounded-full blur-2xl animate-float delay-2000" />
        </div>

        {/* Enhanced Grid pattern overlay */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />

        {/* Gradient overlay for depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Enhanced Profile Image */}
          <div className="mb-8 animate-fade-in-down">
            <div className="relative w-36 h-36 md:w-44 md:h-44 mx-auto">
              {/* Glow effect ring */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-400 via-accent-400 to-primary-400 animate-gradient-xy opacity-75 blur-md"></div>

              {/* Main image container */}
              <div className="relative w-full h-full rounded-full bg-gradient-to-br from-white/30 to-white/10 backdrop-blur-sm border-2 border-white/30 flex items-center justify-center overflow-hidden shadow-2xl">
                {biography?.profile_image_url ? (
                  <img
                    src={biography.profile_image_url}
                    alt={biography.full_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-primary-500 via-accent-500 to-primary-700 flex items-center justify-center text-white text-4xl md:text-5xl font-bold animate-gradient-xy">
                    {biography?.full_name?.charAt(0) || 'س'}
                  </div>
                )}
              </div>

              {/* Floating particles */}
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-accent-400 rounded-full animate-bounce-gentle opacity-80"></div>
              <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-primary-400 rounded-full animate-bounce-gentle delay-500 opacity-80"></div>
            </div>
          </div>

          {/* Enhanced Text Content */}
          <div className="space-y-8 animate-fade-in-up">
            <div className="text-center">
              <p className="text-accent-300 text-lg md:text-xl font-semibold mb-3 tracking-wide">
                {t('home.hero.title')}
              </p>
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-6 text-shadow">
                <span className="bg-gradient-to-r from-white via-accent-200 to-white bg-clip-text text-transparent animate-gradient-x">
                  {biography?.full_name || 'سينمانا'}
                </span>
              </h1>
              <h2 className="text-xl md:text-3xl lg:text-4xl text-accent-200 font-semibold tracking-wide">
                <span className="bg-gradient-to-r from-accent-300 to-primary-300 bg-clip-text text-transparent">
                  {biography?.title || t('home.hero.subtitle')}
                </span>
              </h2>
            </div>

            <p className="text-lg md:text-xl text-white/95 max-w-4xl mx-auto leading-relaxed text-center font-medium">
              {biography?.summary || t('home.hero.description')}
            </p>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-10">
              <Button
                variant="primary"
                size="lg"
                onClick={handleScrollToAbout}
                className="bg-gradient-to-r from-white to-accent-50 text-primary-900 hover:from-accent-50 hover:to-white shadow-2xl hover:shadow-white/25 transform hover:-translate-y-2 hover:scale-105 transition-all duration-500 border-2 border-white/20 backdrop-blur-sm glow-effect"
              >
                {t('home.hero.cta')}
                <ChevronDown size={22} className="mr-2 animate-bounce-gentle" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={handleContactClick}
                className="border-2 border-white/60 text-white hover:bg-gradient-to-r hover:from-white/20 hover:to-accent-200/20 hover:text-white shadow-2xl hover:shadow-accent-500/25 transform hover:-translate-y-2 hover:scale-105 transition-all duration-500 backdrop-blur-sm"
              >
                <Mail size={22} className="ml-2" />
                {t('home.hero.contact')}
              </Button>
            </div>

            {/* CV Download */}
            {biography?.pdf_url && (
              <div className="pt-6">
                <a
                  href={biography.pdf_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-200 hover:text-white transition-colors"
                >
                  <Download size={18} className="ml-2" />
                  {t('home.about.downloadCv')}
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <button
            onClick={handleScrollToAbout}
            className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center"
          >
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse" />
          </button>
        </div>
      </div>

      {/* Loading State */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-primary-900 flex items-center justify-center z-20">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-white/20 border-t-white rounded-full animate-spin mx-auto mb-4" />
            <p className="text-white text-lg">{t('common.loading')}</p>
          </div>
        </div>
      )}
    </section>
  )
}

export default HeroSection
