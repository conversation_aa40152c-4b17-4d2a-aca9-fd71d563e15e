// Database entity types
export interface Biography {
  id: number
  full_name: string
  title?: string
  summary?: string
  description?: string
  email?: string
  phone?: string
  location?: string
  website?: string
  linkedin_url?: string
  github_url?: string
  twitter_url?: string
  pdf_url?: string
  profile_image_url?: string
  created_at: string
  updated_at: string
}

export interface Certificate {
  id: number
  title: string
  issuer: string
  issue_date: string
  expiry_date?: string
  credential_id?: string
  credential_url?: string
  file_url?: string
  description?: string
  skills?: string[]
  is_featured: boolean
  display_order: number
  created_at: string
  updated_at: string
}

export interface Product {
  id: number
  name: string
  description?: string
  short_description?: string
  price?: number
  currency: string
  image_url?: string
  gallery_urls?: string[]
  purchase_link?: string
  demo_link?: string
  github_link?: string
  category?: string
  tags?: string[]
  features?: string[]
  is_featured: boolean
  is_available: boolean
  display_order: number
  created_at: string
  updated_at: string
}

export interface Service {
  id: number
  name: string
  description?: string
  short_description?: string
  price_range?: string
  price_from?: number
  price_to?: number
  currency: string
  icon_name?: string
  image_url?: string
  category?: string
  duration?: string
  deliverables?: string[]
  features?: string[]
  is_featured: boolean
  is_available: boolean
  display_order: number
  created_at: string
  updated_at: string
}

export interface Testimonial {
  id: number
  author: string
  role?: string
  company?: string
  avatar_url?: string
  quote: string
  rating?: number
  project_name?: string
  service_category?: string
  date?: string
  is_featured: boolean
  is_approved: boolean
  display_order: number
  created_at: string
  updated_at: string
}

export interface ContactMessage {
  id: number
  name: string
  email: string
  subject?: string
  message: string
  phone?: string
  company?: string
  service_interest?: string
  status: 'new' | 'read' | 'replied' | 'archived'
  created_at: string
  updated_at: string
}

// API response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  error?: string
}

// Form types
export interface BiographyForm {
  full_name: string
  title?: string
  summary?: string
  description?: string
  email?: string
  phone?: string
  location?: string
  website?: string
  linkedin_url?: string
  github_url?: string
  twitter_url?: string
}

export interface CertificateForm {
  title: string
  issuer: string
  issue_date: string
  expiry_date?: string
  credential_id?: string
  credential_url?: string
  description?: string
  skills?: string[]
  is_featured?: boolean
}

export interface ProductForm {
  name: string
  description?: string
  short_description?: string
  price?: number
  currency?: string
  purchase_link?: string
  demo_link?: string
  github_link?: string
  category?: string
  tags?: string[]
  features?: string[]
  is_featured?: boolean
  is_available?: boolean
}

export interface ServiceForm {
  name: string
  description?: string
  short_description?: string
  price_range?: string
  price_from?: number
  price_to?: number
  currency?: string
  icon_name?: string
  category?: string
  duration?: string
  deliverables?: string[]
  features?: string[]
  is_featured?: boolean
  is_available?: boolean
}

export interface TestimonialForm {
  author: string
  role?: string
  company?: string
  quote: string
  rating?: number
  project_name?: string
  service_category?: string
  date?: string
  is_featured?: boolean
}

export interface ContactForm {
  name: string
  email: string
  subject?: string
  message: string
  phone?: string
  company?: string
  service_interest?: string
}

// Auth types
export interface User {
  id: string
  email: string
  role: string
  created_at: string
  updated_at: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// UI types
export interface NavItem {
  label: string
  href: string
  icon?: string
}

export interface SectionProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

// File upload types
export interface FileUpload {
  file: File
  preview?: string
  progress?: number
  error?: string
}

export interface UploadResponse {
  success: boolean
  url?: string
  error?: string
}
