const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Netlify deployment...');

// Check if Netlify CLI is installed
try {
  execSync('netlify --version', { stdio: 'pipe' });
  console.log('✅ Netlify CLI is available');
} catch (error) {
  console.log('📦 Installing Netlify CLI...');
  execSync('npm install -g netlify-cli', { stdio: 'inherit' });
}

// Build the project
console.log('🔨 Building the project...');
execSync('npm run build', { stdio: 'inherit' });

// Deploy to Netlify
console.log('🌐 Deploying to Netlify...');
try {
  // Deploy the site
  const deployResult = execSync('netlify deploy --prod --dir=dist --open', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('✅ Deployment successful!');
  console.log(deployResult);
  
  // Extract the URL from the output
  const urlMatch = deployResult.match(/Website URL:\s*(https:\/\/[^\s]+)/);
  if (urlMatch) {
    const siteUrl = urlMatch[1];
    console.log(`🌟 Your site is live at: ${siteUrl}`);
    
    // Save the URL for later use
    fs.writeFileSync('SITE_URL.txt', siteUrl);
    
    return siteUrl;
  }
} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  
  // Try manual deployment
  console.log('🔄 Trying manual deployment...');
  try {
    execSync('netlify deploy --prod --dir=dist', { stdio: 'inherit' });
    console.log('✅ Manual deployment completed!');
  } catch (manualError) {
    console.error('❌ Manual deployment also failed:', manualError.message);
    process.exit(1);
  }
}

console.log('🎉 Deployment process completed!');
