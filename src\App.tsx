import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from './store/AuthContext'
import { DataProvider } from './store/DataContext'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import AdminPage from './pages/AdminPage'
import NotFoundPage from './pages/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <div className="App">
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<HomePage />} />
              <Route path="admin/*" element={<AdminPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Route>
          </Routes>
        </div>
      </DataProvider>
    </AuthProvider>
  )
}

export default App
