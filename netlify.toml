[build]
  publish = "dist"
  command = "npm run build"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[functions]]
  directory = "netlify/functions"
  node_bundler = "esbuild"

[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"
  functions = "netlify/functions"

# Identity settings for free tier
[template.environment]
  NETLIFY_IDENTITY_URL = "/.netlify/identity"

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.netlify.com https://*.netlify.app"

# Redirects for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

[[redirects]]
  from = "/admin/*"
  to = "/admin/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# API redirects
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Identity redirects
[[redirects]]
  from = "/.netlify/identity/*"
  to = "/.netlify/identity/:splat"
  status = 200

# Edge functions for rate limiting (free tier compatible)
[[edge_functions]]
  function = "rate-limit"
  path = "/api/*"

# Form handling for contact forms
[forms]
  settings = { spam_protection = true }

# Plugin configuration for free tier
[[plugins]]
  package = "@netlify/plugin-functions-install-core"

# Environment variables template
[template.environment]
  VITE_NETLIFY_SITE_URL = "https://your-site.netlify.app"
  VITE_NETLIFY_IDENTITY_URL = "https://your-site.netlify.app/.netlify/identity"
