import React from 'react'
import { LogIn, Shield } from 'lucide-react'
import { useAuth } from '@/store/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

const AdminLogin: React.FC = () => {
  const { login, error } = useAuth()
  const { t } = useTranslation()

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        <Card className="bg-white/95 backdrop-blur-sm">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-primary-800 rounded-2xl flex items-center justify-center text-white mx-auto mb-4">
              <Shield size={32} />
            </div>
            <h1 className="text-2xl font-bold text-secondary-900 mb-2">
              {t('admin.title')}
            </h1>
            <p className="text-secondary-600">
              قم بتسجيل الدخول للوصول إلى لوحة الإدارة
            </p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl text-red-700 text-sm">
              {error}
            </div>
          )}

          <Button
            variant="primary"
            size="lg"
            onClick={login}
            className="w-full"
          >
            <LogIn size={20} className="ml-2" />
            {t('nav.login')}
          </Button>

          <div className="mt-6 text-center text-sm text-secondary-500">
            <p>سيتم توجيهك إلى صفحة تسجيل الدخول الآمنة</p>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default AdminLogin
