// Contact API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, contactSchema, paginationSchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 10, 15 * 60 * 1000) // Stricter rate limit for contact
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod
    const pathSegments = event.path.split('/').filter(Boolean)
    const messageId = pathSegments[pathSegments.length - 1]

    switch (method) {
      case 'GET':
        // Only admins can view contact messages
        if (messageId && messageId !== 'contact' && !isNaN(Number(messageId))) {
          return await getContactMessage(context, db, Number(messageId), rateLimit)
        }
        return await getContactMessages(event, context, db, rateLimit)
      
      case 'POST':
        return await createContactMessage(event, db, rateLimit)
      
      case 'PUT':
        // Only admins can update message status
        if (!messageId || isNaN(Number(messageId))) {
          return createErrorResponse(400, 'Message ID is required for updates')
        }
        return await updateContactMessage(event, context, db, Number(messageId), rateLimit)
      
      case 'DELETE':
        // Only admins can delete messages
        if (!messageId || isNaN(Number(messageId))) {
          return createErrorResponse(400, 'Message ID is required for deletion')
        }
        return await deleteContactMessage(context, db, Number(messageId), rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Contact API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getContactMessages(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const queryParams = event.queryStringParameters || {}
    
    // Validate pagination parameters
    const paginationValidation = validateInput(paginationSchema, queryParams)
    if (!paginationValidation.success) {
      return createErrorResponse(400, 'Invalid query parameters', paginationValidation.errors)
    }

    const { page = 1, limit = 20, sort = 'desc' } = paginationValidation.data
    const offset = (page - 1) * limit

    // Build query
    let whereClause = ''
    let params: any[] = []
    
    if (queryParams.status) {
      whereClause = 'WHERE status = ?'
      params.push(queryParams.status)
    }

    const orderBy = sort === 'asc' ? 'ASC' : 'DESC'
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM contact_messages ${whereClause}`
    const countResult = await db.prepare(countQuery).bind(...params).first()
    const total = countResult?.total || 0

    // Get messages
    const query = `
      SELECT * FROM contact_messages 
      ${whereClause}
      ORDER BY created_at ${orderBy}
      LIMIT ? OFFSET ?
    `
    const messages = await db.prepare(query).bind(...params, limit, offset).all()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: messages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      })
    }
  } catch (error) {
    console.error('Get contact messages error:', error)
    return createErrorResponse(500, 'Failed to fetch contact messages', error)
  }
}

async function getContactMessage(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const message = await db
      .prepare('SELECT * FROM contact_messages WHERE id = ?')
      .bind(id)
      .first()

    if (!message) {
      return createErrorResponse(404, 'Contact message not found')
    }

    // Mark as read if it's new
    if (message.status === 'new') {
      await db
        .prepare('UPDATE contact_messages SET status = ?, updated_at = ? WHERE id = ?')
        .bind('read', new Date().toISOString(), id)
        .run()
      
      message.status = 'read'
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: message
      })
    }
  } catch (error) {
    console.error('Get contact message error:', error)
    return createErrorResponse(500, 'Failed to fetch contact message', error)
  }
}

async function createContactMessage(event: any, db: any, rateLimit: any) {
  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(contactSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    // Check for spam (simple duplicate check)
    const recentMessage = await db
      .prepare(`
        SELECT id FROM contact_messages 
        WHERE email = ? AND created_at > datetime('now', '-1 hour')
        LIMIT 1
      `)
      .bind(data.email)
      .first()

    if (recentMessage) {
      return createErrorResponse(429, 'Please wait before sending another message')
    }

    const result = await db
      .prepare(`
        INSERT INTO contact_messages (
          name, email, subject, message, phone, company, service_interest,
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'new', ?, ?)
      `)
      .bind(
        data.name,
        data.email,
        data.subject || null,
        data.message,
        data.phone || null,
        data.company || null,
        data.service_interest || null,
        now,
        now
      )
      .run()

    if (!result.success) {
      return createErrorResponse(500, 'Failed to send message')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Message sent successfully. We will get back to you soon!'
      })
    }
  } catch (error) {
    console.error('Create contact message error:', error)
    return createErrorResponse(500, 'Failed to send message', error)
  }
}

async function updateContactMessage(event: any, context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    const { status } = body

    if (!status || !['new', 'read', 'replied', 'archived'].includes(status)) {
      return createErrorResponse(400, 'Invalid status. Must be one of: new, read, replied, archived')
    }

    const now = new Date().toISOString()

    const result = await db
      .prepare('UPDATE contact_messages SET status = ?, updated_at = ? WHERE id = ?')
      .bind(status, now, id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Contact message not found')
    }

    // Fetch updated message
    const updated = await db
      .prepare('SELECT * FROM contact_messages WHERE id = ?')
      .bind(id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: updated,
        message: 'Message status updated successfully'
      })
    }
  } catch (error) {
    console.error('Update contact message error:', error)
    return createErrorResponse(500, 'Failed to update message status', error)
  }
}

async function deleteContactMessage(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM contact_messages WHERE id = ?')
      .bind(id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Contact message not found')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Contact message deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete contact message error:', error)
    return createErrorResponse(500, 'Failed to delete contact message', error)
  }
}
