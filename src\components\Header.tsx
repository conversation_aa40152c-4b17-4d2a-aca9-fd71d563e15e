import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, User } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/store/AuthContext'
import Button from './ui/Button'

const Header: React.FC = () => {
  const { t } = useTranslation()
  const { isAuthenticated, login, logout } = useAuth()
  const location = useLocation()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { label: t('nav.home'), href: '/' },
    { label: t('nav.about'), href: '#about' },
    { label: t('nav.certificates'), href: '#certificates' },
    { label: t('nav.products'), href: '#products' },
    { label: t('nav.services'), href: '#services' },
    { label: t('nav.testimonials'), href: '#testimonials' },
    { label: t('nav.contact'), href: '#contact' },
  ]

  const handleNavClick = (href: string) => {
    setIsMenuOpen(false)
    
    if (href.startsWith('#')) {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-xl shadow-2xl border-b border-primary-200/50'
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Enhanced Logo */}
          <Link
            to="/"
            className="flex items-center space-x-3 text-2xl font-bold hover:scale-105 transition-all duration-300 group"
          >
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 via-accent-600 to-primary-800 rounded-2xl flex items-center justify-center text-white font-bold shadow-lg group-hover:shadow-xl group-hover:shadow-primary-500/25 transition-all duration-300 animate-gradient-xy">
                س
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent-400 rounded-full animate-pulse opacity-80"></div>
            </div>
            <span className={`hidden sm:block transition-all duration-300 ${
              isScrolled
                ? 'bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent'
                : 'text-white'
            }`}>
              سينمانا
            </span>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                onClick={(e) => {
                  if (item.href.startsWith('#')) {
                    e.preventDefault()
                    handleNavClick(item.href)
                  }
                }}
                className={`relative text-sm font-semibold transition-all duration-300 hover:scale-105 group ${
                  location.pathname === item.href || location.hash === item.href
                    ? 'text-primary-600'
                    : isScrolled
                    ? 'text-secondary-700 hover:text-primary-600'
                    : 'text-white hover:text-accent-200'
                }`}
              >
                {item.label}
                <span className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-600 to-accent-600 transition-all duration-300 group-hover:w-full ${
                  location.pathname === item.href || location.hash === item.href ? 'w-full' : ''
                }`}></span>
              </a>
            ))}
          </nav>

          {/* Auth & Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Auth Button */}
            {isAuthenticated ? (
              <div className="hidden sm:flex items-center space-x-3">
                <Link to="/admin">
                  <Button variant="outline" size="sm">
                    <User size={16} className="ml-2" />
                    {t('nav.admin')}
                  </Button>
                </Link>
                <Button variant="ghost" size="sm" onClick={logout}>
                  {t('nav.logout')}
                </Button>
              </div>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={login}
                className="hidden sm:flex"
              >
                {t('nav.login')}
              </Button>
            )}

            {/* Enhanced Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`lg:hidden p-3 rounded-2xl transition-all duration-300 transform hover:scale-110 ${
                isScrolled
                  ? 'text-secondary-700 hover:bg-gradient-to-r hover:from-primary-100 hover:to-accent-100 hover:text-primary-600'
                  : 'text-white hover:bg-white/20 backdrop-blur-sm'
              }`}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-gradient-to-br from-white/95 to-primary-50/95 backdrop-blur-xl border-b-2 border-primary-200/50 shadow-2xl animate-fade-in-down">
            <nav className="py-6 space-y-1">
              {navItems.map((item, index) => (
                <a
                  key={item.href}
                  href={item.href}
                  onClick={(e) => {
                    if (item.href.startsWith('#')) {
                      e.preventDefault()
                    }
                    handleNavClick(item.href)
                  }}
                  className="block px-6 py-4 text-secondary-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-accent-50 transition-all duration-300 font-medium text-lg border-l-4 border-transparent hover:border-primary-600 animate-slide-in-right"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {item.label}
                </a>
              ))}
              
              {/* Enhanced Mobile Auth */}
              <div className="px-6 py-4 border-t-2 border-primary-200/50 mt-4">
                {isAuthenticated ? (
                  <div className="space-y-3">
                    <Link to="/admin" onClick={() => setIsMenuOpen(false)}>
                      <Button variant="outline" size="lg" className="w-full bg-gradient-to-r from-primary-600 to-accent-600 text-white border-none hover:from-primary-700 hover:to-accent-700 shadow-lg transform hover:scale-105 transition-all duration-300">
                        <User size={18} className="ml-2" />
                        {t('nav.admin')}
                      </Button>
                    </Link>
                    <Button
                      variant="ghost"
                      size="lg"
                      onClick={() => {
                        logout()
                        setIsMenuOpen(false)
                      }}
                      className="w-full hover:bg-gradient-to-r hover:from-secondary-100 hover:to-primary-100 transform hover:scale-105 transition-all duration-300"
                    >
                      {t('nav.logout')}
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => {
                      login()
                      setIsMenuOpen(false)
                    }}
                    className="w-full bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 shadow-lg transform hover:scale-105 transition-all duration-300"
                  >
                    {t('nav.login')}
                  </Button>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
