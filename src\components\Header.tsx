import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, User } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/store/AuthContext'
import Button from './ui/Button'

const Header: React.FC = () => {
  const { t } = useTranslation()
  const { isAuthenticated, login, logout } = useAuth()
  const location = useLocation()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { label: t('nav.home'), href: '/' },
    { label: t('nav.about'), href: '#about' },
    { label: t('nav.certificates'), href: '#certificates' },
    { label: t('nav.products'), href: '#products' },
    { label: t('nav.services'), href: '#services' },
    { label: t('nav.testimonials'), href: '#testimonials' },
    { label: t('nav.contact'), href: '#contact' },
  ]

  const handleNavClick = (href: string) => {
    setIsMenuOpen(false)
    
    if (href.startsWith('#')) {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-secondary-200' 
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link 
            to="/" 
            className="flex items-center space-x-3 text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors"
          >
            <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-800 rounded-xl flex items-center justify-center text-white font-bold">
              س
            </div>
            <span className="hidden sm:block">سينمانا</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                onClick={(e) => {
                  if (item.href.startsWith('#')) {
                    e.preventDefault()
                    handleNavClick(item.href)
                  }
                }}
                className={`text-sm font-medium transition-colors hover:text-primary-600 ${
                  location.pathname === item.href || location.hash === item.href
                    ? 'text-primary-600'
                    : isScrolled
                    ? 'text-secondary-700'
                    : 'text-white'
                }`}
              >
                {item.label}
              </a>
            ))}
          </nav>

          {/* Auth & Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Auth Button */}
            {isAuthenticated ? (
              <div className="hidden sm:flex items-center space-x-3">
                <Link to="/admin">
                  <Button variant="outline" size="sm">
                    <User size={16} className="ml-2" />
                    {t('nav.admin')}
                  </Button>
                </Link>
                <Button variant="ghost" size="sm" onClick={logout}>
                  {t('nav.logout')}
                </Button>
              </div>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={login}
                className="hidden sm:flex"
              >
                {t('nav.login')}
              </Button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`lg:hidden p-2 rounded-lg transition-colors ${
                isScrolled
                  ? 'text-secondary-700 hover:bg-secondary-100'
                  : 'text-white hover:bg-white/10'
              }`}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-b border-secondary-200 shadow-lg">
            <nav className="py-4 space-y-2">
              {navItems.map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  onClick={(e) => {
                    if (item.href.startsWith('#')) {
                      e.preventDefault()
                    }
                    handleNavClick(item.href)
                  }}
                  className="block px-4 py-3 text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 transition-colors"
                >
                  {item.label}
                </a>
              ))}
              
              {/* Mobile Auth */}
              <div className="px-4 pt-4 border-t border-secondary-200">
                {isAuthenticated ? (
                  <div className="space-y-2">
                    <Link to="/admin" onClick={() => setIsMenuOpen(false)}>
                      <Button variant="outline" size="sm" className="w-full">
                        <User size={16} className="ml-2" />
                        {t('nav.admin')}
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => {
                        logout()
                        setIsMenuOpen(false)
                      }}
                      className="w-full"
                    >
                      {t('nav.logout')}
                    </Button>
                  </div>
                ) : (
                  <Button 
                    variant="primary" 
                    size="sm" 
                    onClick={() => {
                      login()
                      setIsMenuOpen(false)
                    }}
                    className="w-full"
                  >
                    {t('nav.login')}
                  </Button>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
