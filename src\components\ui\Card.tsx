import React from 'react'
import { clsx } from 'clsx'

interface CardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  gradient?: boolean
  padding?: 'sm' | 'md' | 'lg'
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  gradient = false,
  padding = 'md'
}) => {
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }

  const baseClasses = 'rounded-2xl border border-secondary-200 transition-all duration-300'
  
  const backgroundClasses = gradient
    ? 'bg-gradient-to-br from-white to-secondary-50'
    : 'bg-white'
  
  const hoverClasses = hover
    ? 'hover:shadow-xl hover:shadow-primary-500/10 hover:-translate-y-1 hover:border-primary-200'
    : 'shadow-sm'

  const classes = clsx(
    baseClasses,
    backgroundClasses,
    hoverClasses,
    paddingClasses[padding],
    className
  )

  return (
    <div className={classes}>
      {children}
    </div>
  )
}

export default Card
