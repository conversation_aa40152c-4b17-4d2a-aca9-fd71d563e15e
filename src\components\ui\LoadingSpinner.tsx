import React from 'react'
import { clsx } from 'clsx'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  text?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  text
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  return (
    <div className={clsx('flex flex-col items-center justify-center space-y-3', className)}>
      <div 
        className={clsx(
          'border-4 border-secondary-200 border-t-primary-600 rounded-full animate-spin',
          sizeClasses[size]
        )}
      />
      {text && (
        <p className={clsx('text-secondary-600 font-medium', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  )
}

export default LoadingSpinner
