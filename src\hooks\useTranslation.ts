import { ar } from '@/locales/ar'

type TranslationKey = string
type TranslationParams = Record<string, string | number>

// Simple translation hook for Arabic
export function useTranslation() {
  const t = (key: TranslationKey, params?: TranslationParams): string => {
    // Navigate through nested object using dot notation
    const keys = key.split('.')
    let value: any = ar
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        console.warn(`Translation key not found: ${key}`)
        return key // Return the key if translation not found
      }
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string: ${key}`)
      return key
    }
    
    // Replace parameters in the translation
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match
      })
    }
    
    return value
  }

  return { t }
}
