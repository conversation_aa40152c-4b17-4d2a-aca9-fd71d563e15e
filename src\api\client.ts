import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { ApiResponse, PaginatedResponse } from '@/types'

class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '/.netlify/functions',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getAuthToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          this.clearAuthToken()
          window.location.href = '/admin'
        }
        return Promise.reject(error)
      }
    )
  }

  private getAuthToken(): string | null {
    // Get token from Netlify Identity
    const user = (window as any).netlifyIdentity?.currentUser()
    return user?.token?.access_token || null
  }

  private clearAuthToken(): void {
    // Clear Netlify Identity session
    if ((window as any).netlifyIdentity) {
      (window as any).netlifyIdentity.logout()
    }
  }

  // Generic GET request
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(endpoint, { params })
    return response.data
  }

  // Generic POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(endpoint, data)
    return response.data
  }

  // Generic PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(endpoint, data)
    return response.data
  }

  // Generic DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(endpoint)
    return response.data
  }

  // File upload
  async upload(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.client.post('/api-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// API endpoints
export const api = {
  // Biography
  biography: {
    get: () => apiClient.get<ApiResponse<any>>('/api-biography'),
    update: (data: any) => apiClient.post<ApiResponse<any>>('/api-biography', data),
  },

  // Certificates
  certificates: {
    list: (params?: any) => apiClient.get<PaginatedResponse<any>>('/api-certificates', params),
    get: (id: number) => apiClient.get<ApiResponse<any>>(`/api-certificates/${id}`),
    create: (data: any) => apiClient.post<ApiResponse<any>>('/api-certificates', data),
    update: (id: number, data: any) => apiClient.put<ApiResponse<any>>(`/api-certificates/${id}`, data),
    delete: (id: number) => apiClient.delete<ApiResponse<any>>(`/api-certificates/${id}`),
  },

  // Products
  products: {
    list: (params?: any) => apiClient.get<PaginatedResponse<any>>('/api-products', params),
    get: (id: number) => apiClient.get<ApiResponse<any>>(`/api-products/${id}`),
    create: (data: any) => apiClient.post<ApiResponse<any>>('/api-products', data),
    update: (id: number, data: any) => apiClient.put<ApiResponse<any>>(`/api-products/${id}`, data),
    delete: (id: number) => apiClient.delete<ApiResponse<any>>(`/api-products/${id}`),
  },

  // Services
  services: {
    list: (params?: any) => apiClient.get<PaginatedResponse<any>>('/api-services', params),
    get: (id: number) => apiClient.get<ApiResponse<any>>(`/api-services/${id}`),
    create: (data: any) => apiClient.post<ApiResponse<any>>('/api-services', data),
    update: (id: number, data: any) => apiClient.put<ApiResponse<any>>(`/api-services/${id}`, data),
    delete: (id: number) => apiClient.delete<ApiResponse<any>>(`/api-services/${id}`),
  },

  // Testimonials
  testimonials: {
    list: (params?: any) => apiClient.get<PaginatedResponse<any>>('/api-testimonials', params),
    get: (id: number) => apiClient.get<ApiResponse<any>>(`/api-testimonials/${id}`),
    create: (data: any) => apiClient.post<ApiResponse<any>>('/api-testimonials', data),
    update: (id: number, data: any) => apiClient.put<ApiResponse<any>>(`/api-testimonials/${id}`, data),
    delete: (id: number) => apiClient.delete<ApiResponse<any>>(`/api-testimonials/${id}`),
  },

  // Contact
  contact: {
    list: (params?: any) => apiClient.get<PaginatedResponse<any>>('/api-contact', params),
    get: (id: number) => apiClient.get<ApiResponse<any>>(`/api-contact/${id}`),
    send: (data: any) => apiClient.post<ApiResponse<any>>('/api-contact', data),
    updateStatus: (id: number, status: string) => 
      apiClient.put<ApiResponse<any>>(`/api-contact/${id}`, { status }),
    delete: (id: number) => apiClient.delete<ApiResponse<any>>(`/api-contact/${id}`),
  },

  // File upload
  upload: (file: File, onProgress?: (progress: number) => void) => 
    apiClient.upload(file, onProgress),
}
