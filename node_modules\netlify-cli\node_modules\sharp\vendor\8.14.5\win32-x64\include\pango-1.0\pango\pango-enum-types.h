
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

#ifndef __PANGO_ENUM_TYPES_H__
#define __PANGO_ENUM_TYPES_H__

#include <glib-object.h>

#include <pango/pango-version-macros.h>

G_BEGIN_DECLS

/* enumerations from "pango-attributes.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_attr_type_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_ATTR_TYPE (pango_attr_type_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_underline_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_UNDERLINE (pango_underline_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_overline_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_OVERLINE (pango_overline_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_show_flags_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_SHOW_FLAGS (pango_show_flags_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_text_transform_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_TEXT_TRANSFORM (pango_text_transform_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_baseline_shift_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_BASELINE_SHIFT (pango_baseline_shift_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_font_scale_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_FONT_SCALE (pango_font_scale_get_type ())

/* enumerations from "pango-bidi-type.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_bidi_type_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_BIDI_TYPE (pango_bidi_type_get_type ())

/* enumerations from "pango-coverage.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_coverage_level_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_COVERAGE_LEVEL (pango_coverage_level_get_type ())

/* enumerations from "pango-direction.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_direction_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_DIRECTION (pango_direction_get_type ())

/* enumerations from "pango-font.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_style_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_STYLE (pango_style_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_variant_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_VARIANT (pango_variant_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_weight_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_WEIGHT (pango_weight_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_stretch_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_STRETCH (pango_stretch_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_font_mask_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_FONT_MASK (pango_font_mask_get_type ())

/* enumerations from "pango-glyph.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_shape_flags_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_SHAPE_FLAGS (pango_shape_flags_get_type ())

/* enumerations from "pango-gravity.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_gravity_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_GRAVITY (pango_gravity_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_gravity_hint_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_GRAVITY_HINT (pango_gravity_hint_get_type ())

/* enumerations from "pango-layout.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_alignment_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_ALIGNMENT (pango_alignment_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_wrap_mode_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_WRAP_MODE (pango_wrap_mode_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_ellipsize_mode_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_ELLIPSIZE_MODE (pango_ellipsize_mode_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_layout_serialize_flags_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_LAYOUT_SERIALIZE_FLAGS (pango_layout_serialize_flags_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_layout_deserialize_error_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_LAYOUT_DESERIALIZE_ERROR (pango_layout_deserialize_error_get_type ())
PANGO_AVAILABLE_IN_ALL
GType pango_layout_deserialize_flags_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_LAYOUT_DESERIALIZE_FLAGS (pango_layout_deserialize_flags_get_type ())

/* enumerations from "pango-renderer.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_render_part_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_RENDER_PART (pango_render_part_get_type ())

/* enumerations from "pango-script.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_script_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_SCRIPT (pango_script_get_type ())

/* enumerations from "pango-tabs.h" */
PANGO_AVAILABLE_IN_ALL
GType pango_tab_align_get_type (void) G_GNUC_CONST;
#define PANGO_TYPE_TAB_ALIGN (pango_tab_align_get_type ())
G_END_DECLS

#endif /* __PANGO_ENUM_TYPES_H__ */

/* Generated data ends here */

