// Testimonials API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, testimonialSchema, paginationSchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 100, 15 * 60 * 1000)
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod
    const pathSegments = event.path.split('/').filter(Boolean)
    const testimonialId = pathSegments[pathSegments.length - 1]

    switch (method) {
      case 'GET':
        if (testimonialId && testimonialId !== 'testimonials' && !isNaN(Number(testimonialId))) {
          return await getTestimonial(db, Number(testimonialId), rateLimit)
        }
        return await getTestimonials(event, db, rateLimit)
      
      case 'POST':
        return await createTestimonial(event, context, db, rateLimit)
      
      case 'PUT':
        if (!testimonialId || isNaN(Number(testimonialId))) {
          return createErrorResponse(400, 'Testimonial ID is required for updates')
        }
        return await updateTestimonial(event, context, db, Number(testimonialId), rateLimit)
      
      case 'DELETE':
        if (!testimonialId || isNaN(Number(testimonialId))) {
          return createErrorResponse(400, 'Testimonial ID is required for deletion')
        }
        return await deleteTestimonial(context, db, Number(testimonialId), rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Testimonials API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getTestimonials(event: any, db: any, rateLimit: any) {
  try {
    const queryParams = event.queryStringParameters || {}
    
    // Validate pagination parameters
    const paginationValidation = validateInput(paginationSchema, queryParams)
    if (!paginationValidation.success) {
      return createErrorResponse(400, 'Invalid query parameters', paginationValidation.errors)
    }

    const { page = 1, limit = 10, sort = 'desc', featured } = paginationValidation.data
    const offset = (page - 1) * limit

    // Build query
    let whereClause = 'WHERE is_approved = 1'
    let params: any[] = []
    
    if (featured !== undefined) {
      whereClause += ' AND is_featured = ?'
      params.push(featured)
    }

    const orderBy = sort === 'asc' ? 'ASC' : 'DESC'
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM testimonials ${whereClause}`
    const countResult = await db.prepare(countQuery).bind(...params).first()
    const total = countResult?.total || 0

    // Get testimonials
    const query = `
      SELECT * FROM testimonials 
      ${whereClause}
      ORDER BY is_featured DESC, display_order ASC, date ${orderBy}, created_at ${orderBy}
      LIMIT ? OFFSET ?
    `
    const testimonials = await db.prepare(query).bind(...params, limit, offset).all()

    // Parse JSON fields
    const result = testimonials.map((testimonial: any) => ({
      ...testimonial,
      is_featured: Boolean(testimonial.is_featured),
      is_approved: Boolean(testimonial.is_approved)
    }))

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      })
    }
  } catch (error) {
    console.error('Get testimonials error:', error)
    return createErrorResponse(500, 'Failed to fetch testimonials', error)
  }
}

async function getTestimonial(db: any, id: number, rateLimit: any) {
  try {
    const testimonial = await db
      .prepare('SELECT * FROM testimonials WHERE id = ? AND is_approved = 1')
      .bind(id)
      .first()

    if (!testimonial) {
      return createErrorResponse(404, 'Testimonial not found')
    }

    // Parse JSON fields
    const result = {
      ...testimonial,
      is_featured: Boolean(testimonial.is_featured),
      is_approved: Boolean(testimonial.is_approved)
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    }
  } catch (error) {
    console.error('Get testimonial error:', error)
    return createErrorResponse(500, 'Failed to fetch testimonial', error)
  }
}

async function createTestimonial(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(testimonialSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        INSERT INTO testimonials (
          author, role, company, quote, rating, project_name, service_category,
          date, is_featured, is_approved, display_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0, ?, ?)
      `)
      .bind(
        data.author,
        data.role || null,
        data.company || null,
        data.quote,
        data.rating || null,
        data.project_name || null,
        data.service_category || null,
        data.date || null,
        data.is_featured || false,
        now,
        now
      )
      .run()

    if (!result.success) {
      return createErrorResponse(500, 'Failed to create testimonial')
    }

    // Fetch created testimonial
    const created = await db
      .prepare('SELECT * FROM testimonials WHERE id = ?')
      .bind(result.meta?.last_row_id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...created,
          is_featured: Boolean(created.is_featured),
          is_approved: Boolean(created.is_approved)
        },
        message: 'Testimonial created successfully'
      })
    }
  } catch (error) {
    console.error('Create testimonial error:', error)
    return createErrorResponse(500, 'Failed to create testimonial', error)
  }
}

async function updateTestimonial(event: any, context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(testimonialSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        UPDATE testimonials SET
          author = ?, role = ?, company = ?, quote = ?, rating = ?,
          project_name = ?, service_category = ?, date = ?, is_featured = ?, updated_at = ?
        WHERE id = ?
      `)
      .bind(
        data.author,
        data.role || null,
        data.company || null,
        data.quote,
        data.rating || null,
        data.project_name || null,
        data.service_category || null,
        data.date || null,
        data.is_featured || false,
        now,
        id
      )
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Testimonial not found')
    }

    // Fetch updated testimonial
    const updated = await db
      .prepare('SELECT * FROM testimonials WHERE id = ?')
      .bind(id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...updated,
          is_featured: Boolean(updated.is_featured),
          is_approved: Boolean(updated.is_approved)
        },
        message: 'Testimonial updated successfully'
      })
    }
  } catch (error) {
    console.error('Update testimonial error:', error)
    return createErrorResponse(500, 'Failed to update testimonial', error)
  }
}

async function deleteTestimonial(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM testimonials WHERE id = ?')
      .bind(id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Testimonial not found')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Testimonial deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete testimonial error:', error)
    return createErrorResponse(500, 'Failed to delete testimonial', error)
  }
}
