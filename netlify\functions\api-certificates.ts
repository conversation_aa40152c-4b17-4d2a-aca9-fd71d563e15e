// Certificates API endpoint
import { Handler, Context } from '@netlify/functions'
import { getDatabase } from './utils/database'
import { requireAdmin, createSuccessResponse, createErrorResponse, checkRateLimit, getRateLimitHeaders } from './utils/auth'
import { validateInput, certificateSchema, paginationSchema } from './utils/validation'

export const handler: Handler = async (event, context: Context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      },
      body: '',
    }
  }

  try {
    // Rate limiting
    const clientIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    const rateLimit = checkRateLimit(clientIP, 100, 15 * 60 * 1000)
    
    if (!rateLimit.allowed) {
      return createErrorResponse(429, 'Rate limit exceeded', null)
    }

    const db = getDatabase(context)
    const method = event.httpMethod
    const pathSegments = event.path.split('/').filter(Boolean)
    const certificateId = pathSegments[pathSegments.length - 1]

    switch (method) {
      case 'GET':
        if (certificateId && certificateId !== 'certificates' && !isNaN(Number(certificateId))) {
          return await getCertificate(db, Number(certificateId), rateLimit)
        }
        return await getCertificates(event, db, rateLimit)
      
      case 'POST':
        return await createCertificate(event, context, db, rateLimit)
      
      case 'PUT':
        if (!certificateId || isNaN(Number(certificateId))) {
          return createErrorResponse(400, 'Certificate ID is required for updates')
        }
        return await updateCertificate(event, context, db, Number(certificateId), rateLimit)
      
      case 'DELETE':
        if (!certificateId || isNaN(Number(certificateId))) {
          return createErrorResponse(400, 'Certificate ID is required for deletion')
        }
        return await deleteCertificate(context, db, Number(certificateId), rateLimit)
      
      default:
        return createErrorResponse(405, 'Method not allowed')
    }
  } catch (error) {
    console.error('Certificates API error:', error)
    return createErrorResponse(500, 'Internal server error', error)
  }
}

async function getCertificates(event: any, db: any, rateLimit: any) {
  try {
    const queryParams = event.queryStringParameters || {}
    
    // Validate pagination parameters
    const paginationValidation = validateInput(paginationSchema, queryParams)
    if (!paginationValidation.success) {
      return createErrorResponse(400, 'Invalid query parameters', paginationValidation.errors)
    }

    const { page = 1, limit = 10, sort = 'desc', featured } = paginationValidation.data
    const offset = (page - 1) * limit

    // Build query
    let whereClause = ''
    let params: any[] = []
    
    if (featured !== undefined) {
      whereClause = 'WHERE is_featured = ?'
      params.push(featured)
    }

    const orderBy = sort === 'asc' ? 'ASC' : 'DESC'
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM certificates ${whereClause}`
    const countResult = await db.prepare(countQuery).bind(...params).first()
    const total = countResult?.total || 0

    // Get certificates
    const query = `
      SELECT * FROM certificates 
      ${whereClause}
      ORDER BY is_featured DESC, display_order ASC, issue_date ${orderBy}
      LIMIT ? OFFSET ?
    `
    const certificates = await db.prepare(query).bind(...params, limit, offset).all()

    // Parse JSON fields
    const result = certificates.map((cert: any) => ({
      ...cert,
      skills: cert.skills ? JSON.parse(cert.skills) : [],
      is_featured: Boolean(cert.is_featured)
    }))

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      })
    }
  } catch (error) {
    console.error('Get certificates error:', error)
    return createErrorResponse(500, 'Failed to fetch certificates', error)
  }
}

async function getCertificate(db: any, id: number, rateLimit: any) {
  try {
    const certificate = await db
      .prepare('SELECT * FROM certificates WHERE id = ?')
      .bind(id)
      .first()

    if (!certificate) {
      return createErrorResponse(404, 'Certificate not found')
    }

    // Parse JSON fields
    const result = {
      ...certificate,
      skills: certificate.skills ? JSON.parse(certificate.skills) : [],
      is_featured: Boolean(certificate.is_featured)
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    }
  } catch (error) {
    console.error('Get certificate error:', error)
    return createErrorResponse(500, 'Failed to fetch certificate', error)
  }
}

async function createCertificate(event: any, context: Context, db: any, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(certificateSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        INSERT INTO certificates (
          title, issuer, issue_date, expiry_date, credential_id, credential_url,
          description, skills, is_featured, display_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?)
      `)
      .bind(
        data.title,
        data.issuer,
        data.issue_date,
        data.expiry_date || null,
        data.credential_id || null,
        data.credential_url || null,
        data.description || null,
        data.skills ? JSON.stringify(data.skills) : null,
        data.is_featured || false,
        now,
        now
      )
      .run()

    if (!result.success) {
      return createErrorResponse(500, 'Failed to create certificate')
    }

    // Fetch created certificate
    const created = await db
      .prepare('SELECT * FROM certificates WHERE id = ?')
      .bind(result.meta?.last_row_id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...created,
          skills: created.skills ? JSON.parse(created.skills) : [],
          is_featured: Boolean(created.is_featured)
        },
        message: 'Certificate created successfully'
      })
    }
  } catch (error) {
    console.error('Create certificate error:', error)
    return createErrorResponse(500, 'Failed to create certificate', error)
  }
}

async function updateCertificate(event: any, context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const body = JSON.parse(event.body || '{}')
    
    // Validate input
    const validation = validateInput(certificateSchema, body)
    if (!validation.success) {
      return createErrorResponse(400, 'Validation failed', validation.errors)
    }

    const data = validation.data
    const now = new Date().toISOString()

    const result = await db
      .prepare(`
        UPDATE certificates SET
          title = ?, issuer = ?, issue_date = ?, expiry_date = ?, credential_id = ?,
          credential_url = ?, description = ?, skills = ?, is_featured = ?, updated_at = ?
        WHERE id = ?
      `)
      .bind(
        data.title,
        data.issuer,
        data.issue_date,
        data.expiry_date || null,
        data.credential_id || null,
        data.credential_url || null,
        data.description || null,
        data.skills ? JSON.stringify(data.skills) : null,
        data.is_featured || false,
        now,
        id
      )
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Certificate not found')
    }

    // Fetch updated certificate
    const updated = await db
      .prepare('SELECT * FROM certificates WHERE id = ?')
      .bind(id)
      .first()

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        data: {
          ...updated,
          skills: updated.skills ? JSON.parse(updated.skills) : [],
          is_featured: Boolean(updated.is_featured)
        },
        message: 'Certificate updated successfully'
      })
    }
  } catch (error) {
    console.error('Update certificate error:', error)
    return createErrorResponse(500, 'Failed to update certificate', error)
  }
}

async function deleteCertificate(context: Context, db: any, id: number, rateLimit: any) {
  // Require admin authentication
  const authResult = requireAdmin(context)
  if (!authResult.success) {
    return createErrorResponse(401, authResult.error || 'Authentication required')
  }

  try {
    const result = await db
      .prepare('DELETE FROM certificates WHERE id = ?')
      .bind(id)
      .run()

    if (!result.success || result.meta?.changes === 0) {
      return createErrorResponse(404, 'Certificate not found')
    }

    const headers = getRateLimitHeaders(rateLimit.remaining, rateLimit.resetTime)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        ...headers
      },
      body: JSON.stringify({
        success: true,
        message: 'Certificate deleted successfully'
      })
    }
  } catch (error) {
    console.error('Delete certificate error:', error)
    return createErrorResponse(500, 'Failed to delete certificate', error)
  }
}
