/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_NETLIFY_SITE_URL: string
  readonly VITE_NETLIFY_IDENTITY_URL: string
  readonly VITE_API_BASE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module 'netlify-identity-widget' {
  interface User {
    id: string
    email: string
    app_metadata?: {
      roles?: string[]
    }
    created_at: string
    updated_at: string
    token?: {
      access_token: string
    }
  }

  interface NetlifyIdentity {
    init(config?: { APIUrl?: string }): void
    currentUser(): User | null
    on(event: string, callback: (user?: User | Error) => void): void
    off(event: string, callback?: (user?: User | Error) => void): void
    open(tab?: string): void
    close(): void
    logout(): void
  }

  const netlifyIdentity: NetlifyIdentity
  export default netlifyIdentity
}

declare global {
  interface Window {
    netlifyIdentity?: any
    electronAPI?: {
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
      showMessageBox: (options: any) => Promise<any>
      showOpenDialog: (options: any) => Promise<any>
      showSaveDialog: (options: any) => Promise<any>
      onMenuNew: (callback: () => void) => void
      onMenuSave: (callback: () => void) => void
      onMenuAbout: (callback: () => void) => void
      removeAllListeners: (channel: string) => void
      readFile: (filePath: string) => Promise<string>
      writeFile: (filePath: string, data: string) => Promise<void>
      openExternal: (url: string) => Promise<void>
      isElectron: boolean
    }
    windowControls?: {
      minimize: () => Promise<void>
      maximize: () => Promise<void>
      close: () => Promise<void>
      isMaximized: () => Promise<boolean>
    }
  }
}
