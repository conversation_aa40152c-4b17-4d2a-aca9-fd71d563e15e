// Authentication utilities for Netlify Identity
import { Context } from '@netlify/functions'
import jwt from 'jsonwebtoken'

export interface NetlifyUser {
  id: string
  email: string
  role?: string
  app_metadata?: {
    roles?: string[]
  }
  user_metadata?: any
}

export interface AuthResult {
  success: boolean
  user?: NetlifyUser
  error?: string
}

export function verifyNetlifyIdentity(context: Context): AuthResult {
  try {
    // Get the authorization header
    const authHeader = context.event.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'No authorization token provided' }
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // In development, we'll use a mock user
    if (process.env.NODE_ENV === 'development') {
      return {
        success: true,
        user: {
          id: 'dev-user-1',
          email: '<EMAIL>',
          role: 'admin',
          app_metadata: { roles: ['admin'] }
        }
      }
    }

    // Verify JWT token (Netlify Identity tokens are JWTs)
    const decoded = jwt.decode(token) as any
    if (!decoded) {
      return { success: false, error: 'Invalid token' }
    }

    // Extract user information from token
    const user: NetlifyUser = {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.app_metadata?.roles?.[0] || 'user',
      app_metadata: decoded.app_metadata,
      user_metadata: decoded.user_metadata
    }

    return { success: true, user }
  } catch (error) {
    console.error('Auth verification error:', error)
    return { success: false, error: 'Authentication failed' }
  }
}

export function requireAuth(context: Context): AuthResult {
  const authResult = verifyNetlifyIdentity(context)
  if (!authResult.success) {
    return authResult
  }
  return authResult
}

export function requireAdmin(context: Context): AuthResult {
  const authResult = requireAuth(context)
  if (!authResult.success) {
    return authResult
  }

  const user = authResult.user!
  const isAdmin = user.role === 'admin' || 
                  user.app_metadata?.roles?.includes('admin')

  if (!isAdmin) {
    return { success: false, error: 'Admin access required' }
  }

  return authResult
}

export function createResponse(
  statusCode: number,
  body: any,
  headers: Record<string, string> = {}
) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      ...headers
    },
    body: JSON.stringify(body)
  }
}

export function createErrorResponse(
  statusCode: number,
  message: string,
  error?: any
) {
  console.error('API Error:', message, error)
  return createResponse(statusCode, {
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && error && { details: error })
  })
}

export function createSuccessResponse(data: any, message?: string) {
  return createResponse(200, {
    success: true,
    data,
    ...(message && { message })
  })
}

// Rate limiting utilities
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const key = identifier
  
  let record = rateLimitStore.get(key)
  
  if (!record || now > record.resetTime) {
    record = {
      count: 1,
      resetTime: now + windowMs
    }
    rateLimitStore.set(key, record)
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: record.resetTime
    }
  }
  
  record.count++
  
  if (record.count > maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    }
  }
  
  return {
    allowed: true,
    remaining: maxRequests - record.count,
    resetTime: record.resetTime
  }
}

export function getRateLimitHeaders(
  remaining: number,
  resetTime: number
): Record<string, string> {
  return {
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString()
  }
}
