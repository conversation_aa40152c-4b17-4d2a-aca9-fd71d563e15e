import React, { useEffect } from 'react'
import { Award, ExternalLink } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import { api } from '@/api/client'
import Card from '@/components/ui/Card'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

const CertificatesSection: React.FC = () => {
  const { t } = useTranslation()
  const { state, dispatch } = useData()

  useEffect(() => {
    const loadCertificates = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'certificates', loading: true } })
        const response = await api.certificates.list({ featured: true, limit: 6 })
        if (response.success) {
          dispatch({ type: 'SET_CERTIFICATES', payload: response.data })
        }
      } catch (error) {
        console.error('Failed to load certificates:', error)
        dispatch({ type: 'SET_ERROR', payload: { key: 'certificates', error: 'Failed to load certificates' } })
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'certificates', loading: false } })
      }
    }

    loadCertificates()
  }, [dispatch])

  if (state.loading.certificates) {
    return (
      <section id="certificates" className="section bg-white">
        <div className="container">
          <LoadingSpinner size="lg" text={t('common.loading')} />
        </div>
      </section>
    )
  }

  return (
    <section id="certificates" className="section bg-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            {t('home.certificates.title')}
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            {t('home.certificates.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {state.certificates.map((certificate) => (
            <Card key={certificate.id} hover className="group">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-800 rounded-xl flex items-center justify-center text-white flex-shrink-0">
                  <Award size={24} />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors">
                    {certificate.title}
                  </h3>
                  <p className="text-primary-600 font-medium mb-2">
                    {certificate.issuer}
                  </p>
                  <p className="text-sm text-secondary-500 mb-3">
                    {new Date(certificate.issue_date).toLocaleDateString('ar-SA')}
                  </p>
                  {certificate.description && (
                    <p className="text-secondary-700 text-sm leading-relaxed mb-4">
                      {certificate.description}
                    </p>
                  )}
                  {certificate.credential_url && (
                    <a
                      href={certificate.credential_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm font-medium"
                    >
                      <ExternalLink size={16} className="ml-1" />
                      عرض الشهادة
                    </a>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {state.certificates.length === 0 && (
          <div className="text-center py-12">
            <Award size={48} className="mx-auto text-secondary-400 mb-4" />
            <p className="text-secondary-600">لا توجد شهادات متاحة حالياً</p>
          </div>
        )}
      </div>
    </section>
  )
}

export default CertificatesSection
