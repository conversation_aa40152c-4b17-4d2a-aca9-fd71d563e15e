import React, { useEffect } from 'react'
import { Award, ExternalLink } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useData } from '@/store/DataContext'
import { api } from '@/api/client'
import Card from '@/components/ui/Card'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

const CertificatesSection: React.FC = () => {
  const { t } = useTranslation()
  const { state, dispatch } = useData()

  useEffect(() => {
    const loadCertificates = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'certificates', loading: true } })
        const response = await api.certificates.list({ featured: true, limit: 6 })
        if (response.success) {
          dispatch({ type: 'SET_CERTIFICATES', payload: response.data })
        }
      } catch (error) {
        console.error('Failed to load certificates:', error)
        dispatch({ type: 'SET_ERROR', payload: { key: 'certificates', error: 'Failed to load certificates' } })
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'certificates', loading: false } })
      }
    }

    loadCertificates()
  }, [dispatch])

  if (state.loading.certificates) {
    return (
      <section id="certificates" className="section bg-white">
        <div className="container">
          <LoadingSpinner size="lg" text={t('common.loading')} />
        </div>
      </section>
    )
  }

  return (
    <section id="certificates" className="section bg-gradient-to-br from-white via-accent-50/30 to-primary-50/30 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-32 right-20 w-64 h-64 bg-gradient-to-r from-primary-200/20 to-accent-200/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 left-20 w-80 h-80 bg-gradient-to-r from-accent-200/20 to-primary-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container relative z-10">
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-accent-600 to-primary-800 bg-clip-text text-transparent">
              {t('home.certificates.title')}
            </span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            {t('home.certificates.subtitle')}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-accent-600 mx-auto mt-6 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {state.certificates.map((certificate, index) => (
            <Card
              key={certificate.id}
              hover
              className={`group bg-gradient-to-br from-white to-primary-50/50 border-2 border-primary-200/50 shadow-xl hover:shadow-2xl hover:shadow-primary-500/20 transform hover:-translate-y-2 transition-all duration-500 animate-fade-in-up`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="space-y-6">
                {/* Header with icon */}
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-600 via-accent-600 to-primary-800 rounded-2xl flex items-center justify-center text-white flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Award size={28} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-secondary-900 mb-1 group-hover:text-primary-600 transition-colors leading-tight">
                      {certificate.title}
                    </h3>
                    <p className="text-lg font-semibold">
                      <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
                        {certificate.issuer}
                      </span>
                    </p>
                  </div>
                </div>

                {/* Date */}
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-primary-600 to-accent-600 rounded-full"></div>
                  <p className="text-sm font-medium text-secondary-600">
                    {new Date(certificate.issue_date).toLocaleDateString('ar-SA')}
                  </p>
                </div>

                {/* Description */}
                {certificate.description && (
                  <p className="text-secondary-700 leading-relaxed">
                    {certificate.description}
                  </p>
                )}

                {/* Action button */}
                {certificate.credential_url && (
                  <a
                    href={certificate.credential_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-600 to-accent-600 text-white rounded-xl hover:from-primary-700 hover:to-accent-700 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 shadow-lg hover:shadow-xl font-medium"
                  >
                    <ExternalLink size={18} className="ml-2" />
                    عرض الشهادة
                  </a>
                )}
              </div>
            </Card>
          ))}
        </div>

        {state.certificates.length === 0 && (
          <div className="text-center py-12">
            <Award size={48} className="mx-auto text-secondary-400 mb-4" />
            <p className="text-secondary-600">لا توجد شهادات متاحة حالياً</p>
          </div>
        )}
      </div>
    </section>
  )
}

export default CertificatesSection
