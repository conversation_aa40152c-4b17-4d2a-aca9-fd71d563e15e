# 🚀 دليل النشر - موقع سينمانا الشخصي وتطبيق إدارة المحتوى

## 📋 المتطلبات الأساسية

- حساب GitHub
- حساب Netlify (مجاني)
- Node.js 18+ و npm 9+
- Git

## 🌐 نشر الموقع على Netlify

### 1. إعد<PERSON> المستودع

```bash
# إنشاء مستودع جديد على GitHub
git init
git add .
git commit -m "Initial commit: Complete Cinmana website with Arabic support"
git branch -M main
git remote add origin https://github.com/YOUR_USERNAME/cinmana-website.git
git push -u origin main
```

### 2. ربط Netlify بـ GitHub

1. اذهب إلى [netlify.com](https://netlify.com) وسجل دخولك
2. انقر على "New site from Git"
3. اختر GitHub وصل حسابك
4. اختر مستودع `cinmana-website`
5. اض<PERSON><PERSON> إعدادات البناء:
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`
   - **Functions directory**: `netlify/functions`

### 3. تكوين متغيرات البيئة

في لوحة تحكم Netlify، اذهب إلى Site Settings > Environment Variables وأضف:

```
VITE_NETLIFY_SITE_URL=https://your-site-name.netlify.app
VITE_NETLIFY_IDENTITY_URL=https://your-site-name.netlify.app/.netlify/identity
VITE_API_BASE_URL=/.netlify/functions
NODE_ENV=production
```

### 4. تفعيل Netlify Identity

1. اذهب إلى Site Settings > Identity
2. انقر على "Enable Identity"
3. في Registration preferences، اختر "Invite only"
4. في External providers، يمكنك تفعيل Google أو GitHub (اختياري)
5. في Git Gateway، انقر على "Enable Git Gateway"

### 5. إنشاء مستخدم إداري

1. اذهب إلى Identity tab في لوحة التحكم
2. انقر على "Invite users"
3. أدخل بريدك الإلكتروني
4. ستصلك دعوة عبر البريد الإلكتروني
5. اتبع الرابط وأنشئ كلمة مرور

### 6. تكوين قاعدة البيانات D1

قاعدة البيانات ستُنشأ تلقائياً عند أول استخدام للموقع. البيانات التجريبية ستُضاف من ملف `database/schema.sql`.

## 🖥️ بناء تطبيق Electron المكتبي

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. بناء التطبيق

```bash
# بناء الموقع أولاً
npm run build

# بناء تطبيق Electron
npm run package-electron
```

### 3. العثور على ملف التثبيت

ستجد ملف `.exe` في مجلد `dist-electron/`:
- `Cinmana CMS Setup 1.0.0.exe` (Windows)

## 🔧 اختبار النظام

### 1. اختبار الموقع

1. افتح رابط موقعك على Netlify
2. تأكد من تحميل جميع الأقسام بشكل صحيح
3. اختبر النموذج في قسم "تواصل معي"
4. تأكد من عمل التصميم المتجاوب على الهاتف

### 2. اختبار لوحة الإدارة

1. اذهب إلى `your-site.netlify.app/admin`
2. سجل دخولك بالحساب الذي أنشأته
3. تأكد من ظهور لوحة الإدارة بشكل صحيح
4. اختبر إضافة وتعديل المحتوى

### 3. اختبار تطبيق Electron

1. ثبت التطبيق من ملف `.exe`
2. افتح التطبيق
3. سجل دخولك بنفس حساب الإدارة
4. تأكد من مزامنة البيانات مع الموقع

## 🎨 تخصيص المحتوى

### 1. تحديث المعلومات الشخصية

1. اذهب إلى لوحة الإدارة
2. انقر على "السيرة الذاتية"
3. أدخل معلوماتك الشخصية
4. ارفع صورتك الشخصية
5. احفظ التغييرات

### 2. إضافة الشهادات

1. في لوحة الإدارة، انقر على "الشهادات"
2. انقر على "إضافة شهادة جديدة"
3. املأ تفاصيل الشهادة
4. ارفع ملف الشهادة (PDF)
5. احفظ

### 3. إضافة المنتجات والخدمات

اتبع نفس العملية لإضافة منتجاتك وخدماتك.

## 🔒 الأمان

### إعدادات الأمان المطبقة:

- ✅ مصادقة JWT عبر Netlify Identity
- ✅ تحديد معدل الطلبات (Rate Limiting)
- ✅ التحقق من صحة البيانات
- ✅ رؤوس الأمان (Security Headers)
- ✅ HTTPS إجباري
- ✅ حماية CORS

## 📱 الاستجابة والتوافق

الموقع متوافق مع:
- ✅ جميع المتصفحات الحديثة
- ✅ الهواتف الذكية والأجهزة اللوحية
- ✅ أجهزة سطح المكتب
- ✅ قارئات الشاشة (Accessibility)

## 🌍 الدعم متعدد اللغات

- ✅ دعم كامل للغة العربية
- ✅ تخطيط RTL (من اليمين لليسار)
- ✅ خطوط عربية جميلة (Cairo, Tajawal)
- ✅ تنسيق التواريخ والأرقام بالعربية

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. الموقع لا يتحمل
- تأكد من صحة إعدادات البناء في Netlify
- تحقق من سجلات البناء (Build logs)
- تأكد من وجود جميع الملفات المطلوبة

#### 2. لوحة الإدارة لا تعمل
- تأكد من تفعيل Netlify Identity
- تحقق من إنشاء المستخدم الإداري
- تأكد من صحة متغيرات البيئة

#### 3. قاعدة البيانات لا تعمل
- تحقق من سجلات Functions في Netlify
- تأكد من صحة مخطط قاعدة البيانات
- راجع إعدادات D1

#### 4. تطبيق Electron لا يعمل
- تأكد من بناء الموقع أولاً (`npm run build`)
- تحقق من إصدار Node.js (يجب أن يكون 18+)
- تأكد من صحة إعدادات Electron

## 📞 الدعم

إذا واجهت أي مشاكل:

1. راجع سجلات الأخطاء في Netlify
2. تحقق من وحدة التحكم في المتصفح
3. تأكد من اتباع جميع خطوات النشر
4. راجع ملف README.md للمزيد من التفاصيل

## 🎉 تهانينا!

موقعك الشخصي وتطبيق إدارة المحتوى جاهزان الآن! 

**روابط مهمة:**
- 🌐 موقعك: `https://your-site.netlify.app`
- 🔧 لوحة الإدارة: `https://your-site.netlify.app/admin`
- 📱 تطبيق سطح المكتب: ملف `.exe` في مجلد `dist-electron`

استمتع بموقعك الجديد! 🚀
