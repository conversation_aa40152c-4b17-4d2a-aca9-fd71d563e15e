import React, { useState } from 'react'
import { Send, Mail, Phone, MapPin, CheckCircle } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { api } from '@/api/client'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Textarea from '@/components/ui/Textarea'
import Card from '@/components/ui/Card'

const ContactSection: React.FC = () => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    phone: '',
    company: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      const response = await api.contact.send(formData)
      if (response.success) {
        setIsSubmitted(true)
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          phone: '',
          company: ''
        })
      }
    } catch (err: any) {
      setError(err.response?.data?.error || t('errors.generic'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <section id="contact" className="section bg-gradient-to-br from-secondary-900 to-primary-900 text-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            {t('home.contact.title')}
          </h2>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            {t('home.contact.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            {isSubmitted ? (
              <div className="text-center py-8">
                <CheckCircle size={64} className="mx-auto text-green-400 mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">
                  تم إرسال الرسالة بنجاح!
                </h3>
                <p className="text-white/80">
                  {t('success.messageSent')}
                </p>
                <Button
                  variant="outline"
                  className="mt-6 border-white text-white hover:bg-white hover:text-primary-900"
                  onClick={() => setIsSubmitted(false)}
                >
                  إرسال رسالة أخرى
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={t('home.contact.form.name')}
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="bg-white/10 border-white/20 text-white placeholder-white/60"
                  />
                  <Input
                    label={t('home.contact.form.email')}
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="bg-white/10 border-white/20 text-white placeholder-white/60"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={t('common.phone')}
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder-white/60"
                  />
                  <Input
                    label={t('home.contact.form.company')}
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className="bg-white/10 border-white/20 text-white placeholder-white/60"
                  />
                </div>

                <Input
                  label={t('home.contact.form.subject')}
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className="bg-white/10 border-white/20 text-white placeholder-white/60"
                />

                <Textarea
                  label={t('home.contact.form.message')}
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="bg-white/10 border-white/20 text-white placeholder-white/60"
                />

                {error && (
                  <div className="text-red-400 text-sm">{error}</div>
                )}

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  loading={isSubmitting}
                  className="w-full bg-white text-primary-900 hover:bg-primary-50"
                >
                  <Send size={20} className="ml-2" />
                  {t('home.contact.form.send')}
                </Button>
              </form>
            )}
          </Card>

          {/* Contact Info */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold mb-6">معلومات التواصل</h3>
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center">
                    <Mail size={24} className="text-primary-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">البريد الإلكتروني</h4>
                    <p className="text-white/80"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center">
                    <Phone size={24} className="text-primary-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">الهاتف</h4>
                    <p className="text-white/80">+****************</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center">
                    <MapPin size={24} className="text-primary-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">الموقع</h4>
                    <p className="text-white/80">مدينتك، بلدك</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h4 className="text-lg font-bold text-white mb-4">أوقات العمل</h4>
              <div className="space-y-2 text-white/80">
                <div className="flex justify-between">
                  <span>الأحد - الخميس</span>
                  <span>9:00 ص - 6:00 م</span>
                </div>
                <div className="flex justify-between">
                  <span>الجمعة - السبت</span>
                  <span>مغلق</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ContactSection
