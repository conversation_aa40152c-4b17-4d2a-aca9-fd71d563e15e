import React, { useState } from 'react'
import { Send, Mail, Phone, MapPin, CheckCircle } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { api } from '@/api/client'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Textarea from '@/components/ui/Textarea'
import Card from '@/components/ui/Card'

const ContactSection: React.FC = () => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    phone: '',
    company: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      const response = await api.contact.send(formData)
      if (response.success) {
        setIsSubmitted(true)
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          phone: '',
          company: ''
        })
      }
    } catch (err: any) {
      setError(err.response?.data?.error || t('errors.generic'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <section id="contact" className="section bg-gradient-to-br from-secondary-900 via-primary-900 to-accent-900 text-white relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-primary-600/20 to-accent-600/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-accent-600/20 to-primary-600/20 rounded-full blur-3xl animate-float delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-primary-500/10 to-accent-500/10 rounded-full blur-3xl animate-float delay-500"></div>
      </div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }} />

      <div className="container relative z-10">
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white via-accent-200 to-white bg-clip-text text-transparent animate-gradient-x">
              {t('home.contact.title')}
            </span>
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            {t('home.contact.subtitle')}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-accent-400 to-primary-400 mx-auto mt-6 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Enhanced Contact Form */}
          <Card className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md border-2 border-white/30 shadow-2xl hover:shadow-white/10 transition-all duration-500">
            {isSubmitted ? (
              <div className="text-center py-12 animate-scale-in">
                <div className="relative mb-6">
                  <CheckCircle size={80} className="mx-auto text-success-400 animate-bounce-gentle" />
                  <div className="absolute inset-0 bg-success-400/20 rounded-full blur-xl animate-pulse"></div>
                </div>
                <h3 className="text-3xl font-bold text-white mb-4">
                  تم إرسال الرسالة بنجاح!
                </h3>
                <p className="text-white/90 text-lg leading-relaxed mb-8">
                  {t('success.messageSent')}
                </p>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-white/60 text-white hover:bg-gradient-to-r hover:from-white/20 hover:to-accent-200/20 hover:text-white shadow-xl transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
                  onClick={() => setIsSubmitted(false)}
                >
                  إرسال رسالة أخرى
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-8 animate-fade-in-up">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-white/90 mb-2">
                      {t('home.contact.form.name')} <span className="text-accent-400">*</span>
                    </label>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                      placeholder="اسمك الكامل"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-white/90 mb-2">
                      {t('home.contact.form.email')} <span className="text-accent-400">*</span>
                    </label>
                    <Input
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-white/90 mb-2">
                      {t('common.phone')}
                    </label>
                    <Input
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                      placeholder="+966 50 123 4567"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-white/90 mb-2">
                      {t('home.contact.form.company')}
                    </label>
                    <Input
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                      placeholder="اسم الشركة"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-white/90 mb-2">
                    {t('home.contact.form.subject')}
                  </label>
                  <Input
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                    placeholder="موضوع الرسالة"
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-white/90 mb-2">
                    {t('home.contact.form.message')} <span className="text-accent-400">*</span>
                  </label>
                  <Textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-accent-400 focus:ring-accent-400 hover:bg-white/20 transition-all duration-300"
                    placeholder="اكتب رسالتك هنا..."
                  />
                </div>

                {error && (
                  <div className="p-4 bg-error-500/20 border border-error-400/50 rounded-xl text-error-200 text-sm backdrop-blur-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  loading={isSubmitting}
                  className="w-full bg-gradient-to-r from-white to-accent-50 text-primary-900 hover:from-accent-50 hover:to-white shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1 hover:scale-105 transition-all duration-500 border-2 border-white/20 backdrop-blur-sm font-bold text-lg py-4"
                >
                  <Send size={24} className="ml-3" />
                  {t('home.contact.form.send')}
                </Button>
              </form>
            )}
          </Card>

          {/* Enhanced Contact Info */}
          <div className="space-y-10 animate-slide-in-left">
            <div>
              <h3 className="text-3xl font-bold mb-8">
                <span className="bg-gradient-to-r from-white via-accent-200 to-white bg-clip-text text-transparent">
                  معلومات التواصل
                </span>
              </h3>
              <div className="space-y-8">
                <div className="flex items-center space-x-6 p-6 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                  <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Mail size={28} className="text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-white text-lg mb-1">البريد الإلكتروني</h4>
                    <p className="text-white/90 text-lg"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center space-x-6 p-6 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                  <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-primary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Phone size={28} className="text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-white text-lg mb-1">الهاتف</h4>
                    <p className="text-white/90 text-lg">+966 50 123 4567</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6 p-6 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                  <div className="w-16 h-16 bg-gradient-to-r from-secondary-500 to-primary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <MapPin size={28} className="text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-white text-lg mb-1">الموقع</h4>
                    <p className="text-white/90 text-lg">الرياض، المملكة العربية السعودية</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md rounded-3xl p-8 border-2 border-white/30 shadow-2xl hover:shadow-white/10 transition-all duration-500">
              <h4 className="text-2xl font-bold text-white mb-6 text-center">
                <span className="bg-gradient-to-r from-accent-200 to-primary-200 bg-clip-text text-transparent">
                  أوقات العمل
                </span>
              </h4>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-white/10 rounded-xl border border-white/20">
                  <span className="font-semibold text-white">الأحد - الخميس</span>
                  <span className="text-accent-200 font-bold">9:00 ص - 6:00 م</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-white/10 rounded-xl border border-white/20">
                  <span className="font-semibold text-white">الجمعة - السبت</span>
                  <span className="text-secondary-300 font-bold">مغلق</span>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="flex justify-center mt-6 space-x-2">
                <div className="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse delay-200"></div>
                <div className="w-2 h-2 bg-accent-400 rounded-full animate-pulse delay-400"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ContactSection
