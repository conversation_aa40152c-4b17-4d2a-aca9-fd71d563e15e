{"name": "cinmana-personal-website", "version": "1.0.0", "type": "module", "description": "Personal website with desktop CMS using Netlify and Electron", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "build-functions": "netlify-cli build", "dev-functions": "netlify dev", "electron": "electron electron/main.js", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron electron/main.js\"", "electron-build": "electron-builder", "package-electron": "npm run build && electron-builder", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "keywords": ["personal-website", "cms", "netlify", "electron", "react", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"@hookform/resolvers": "^3.3.2", "axios": "^1.6.2", "clsx": "^2.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "netlify-identity-widget": "^1.9.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.0.0", "yup": "^1.3.3", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "husky": "^8.0.3", "lint-staged": "^15.1.0", "netlify-cli": "^17.38.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "wait-on": "^7.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "build": {"appId": "com.cinmana.cms", "productName": "Cinmana CMS", "directories": {"output": "dist-electron"}, "files": ["electron/**/*", "dist/**/*", "package.json"], "extraResources": [{"from": "dist", "to": "app/dist"}], "win": {"target": "nsis"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}