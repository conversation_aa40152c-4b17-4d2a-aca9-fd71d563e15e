@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    direction: rtl;
  }

  body {
    @apply font-sans text-secondary-900 bg-white;
    font-family: 'Cairo', 'Taja<PERSON>', system-ui, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-secondary-900;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl;
  }
  
  h5 {
    @apply text-lg md:text-xl;
  }
  
  h6 {
    @apply text-base md:text-lg;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden;
  }

  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500 shadow-lg hover:shadow-xl hover:shadow-primary-500/25 transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply btn bg-gradient-to-r from-secondary-600 to-secondary-700 text-white hover:from-secondary-700 hover:to-secondary-800 focus:ring-secondary-500 shadow-lg hover:shadow-xl hover:shadow-secondary-500/25 transform hover:-translate-y-0.5;
  }

  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-gradient-to-r hover:from-primary-600 hover:to-primary-700 hover:text-white focus:ring-primary-500 transform hover:-translate-y-0.5 hover:border-transparent;
  }

  .btn-ghost {
    @apply btn text-secondary-600 hover:bg-gradient-to-r hover:from-secondary-100 hover:to-secondary-200 focus:ring-secondary-500 transform hover:-translate-y-0.5;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .input {
    @apply block w-full px-4 py-3 border border-secondary-300 rounded-xl shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 bg-white hover:border-secondary-400;
  }

  .textarea {
    @apply input min-h-[120px];
    resize: vertical;
  }

  .select {
    @apply input pr-10 bg-white;
  }

  .label {
    @apply block text-sm font-semibold text-secondary-700 mb-2;
  }

  .card {
    @apply bg-white rounded-2xl shadow-lg border border-secondary-200/50 p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/10;
  }
  
  .section {
    @apply py-16 md:py-24;
  }
  
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-accent-600 to-primary-800 bg-clip-text text-transparent;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-accent-50;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md border border-white/20;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-secondary-200 border-t-primary-600;
  }

  .hover-lift {
    @apply transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  }

  .glow-effect {
    @apply shadow-lg shadow-primary-500/25 hover:shadow-xl hover:shadow-primary-500/40 transition-all duration-300;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .border-gradient {
    @apply relative;
  }

  .border-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg, #6366f1, #d946ef, #6366f1);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}
