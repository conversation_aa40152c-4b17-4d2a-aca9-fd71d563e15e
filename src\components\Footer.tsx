import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Mail, Phone, MapPin, Github, Linkedin, Twitter } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'

const Footer: React.FC = () => {
  const { t } = useTranslation()

  const socialLinks = [
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Twitter, href: '#', label: 'Twitter' },
  ]

  const quickLinks = [
    { label: t('nav.about'), href: '#about' },
    { label: t('nav.services'), href: '#services' },
    { label: t('nav.products'), href: '#products' },
    { label: t('nav.contact'), href: '#contact' },
  ]

  return (
    <footer className="bg-gradient-to-br from-secondary-900 to-secondary-800 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <Link 
              to="/" 
              className="flex items-center space-x-3 text-2xl font-bold text-white hover:text-primary-400 transition-colors"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-800 rounded-xl flex items-center justify-center text-white font-bold">
                س
              </div>
              <span>سينمانا</span>
            </Link>
            <p className="text-secondary-300 leading-relaxed">
              مطور ومصمم شغوف بإنشاء تجارب رقمية استثنائية تجمع بين التصميم الجميل والتكنولوجيا المتطورة.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">روابط سريعة</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    onClick={(e) => {
                      if (link.href.startsWith('#')) {
                        e.preventDefault()
                        const element = document.querySelector(link.href)
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' })
                        }
                      }
                    }}
                    className="text-secondary-300 hover:text-primary-400 transition-colors"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">{t('home.contact.info.email')}</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-secondary-300">
                <Mail size={18} className="text-primary-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-secondary-300">
                <Phone size={18} className="text-primary-400" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-3 text-secondary-300">
                <MapPin size={18} className="text-primary-400" />
                <span>مدينتك، بلدك</span>
              </div>
            </div>
          </div>

          {/* Social Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">{t('home.contact.info.social')}</h3>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label={social.label}
                >
                  <social.icon size={20} className="text-secondary-300 group-hover:text-white" />
                </a>
              ))}
            </div>
            
            {/* Newsletter */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-white mb-2">اشترك في النشرة الإخبارية</h4>
              <div className="flex space-x-2">
                <input
                  type="email"
                  placeholder="بريدك الإلكتروني"
                  className="flex-1 px-3 py-2 bg-secondary-800 border border-secondary-700 rounded-lg text-white placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
                <button className="px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg text-white font-medium transition-colors">
                  اشتراك
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-secondary-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-secondary-400 text-sm">
              © {new Date().getFullYear()} سينمانا. جميع الحقوق محفوظة.
            </p>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-secondary-400 hover:text-primary-400 transition-colors">
                سياسة الخصوصية
              </a>
              <a href="#" className="text-secondary-400 hover:text-primary-400 transition-colors">
                شروط الاستخدام
              </a>
              <a href="#" className="text-secondary-400 hover:text-primary-400 transition-colors">
                اتصل بنا
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
