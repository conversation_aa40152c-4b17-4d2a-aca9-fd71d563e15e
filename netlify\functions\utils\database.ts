// Database utility functions for Netlify D1
import { Context } from '@netlify/functions'

// Note: In production, Netlify D1 will be available via context.env
// For development, we'll use a local SQLite database

export interface DatabaseConnection {
  prepare: (query: string) => {
    bind: (...params: any[]) => {
      first: () => Promise<any>
      all: () => Promise<any[]>
      run: () => Promise<{ success: boolean; meta?: any }>
    }
    first: () => Promise<any>
    all: () => Promise<any[]>
    run: () => Promise<{ success: boolean; meta?: any }>
  }
  exec: (query: string) => Promise<{ success: boolean; meta?: any }>
}

export function getDatabase(context: Context): DatabaseConnection {
  // In production, this will be context.env.DB (Netlify D1)
  // For development, we'll simulate the D1 interface
  if (process.env.NODE_ENV === 'development') {
    return createMockDatabase()
  }
  
  // Production D1 database
  return (context as any).env?.DB || createMockDatabase()
}

function createMockDatabase(): DatabaseConnection {
  // Mock database for development
  const mockData = {
    biography: [
      {
        id: 1,
        full_name: 'Your Name',
        title: 'Full Stack Developer',
        summary: 'Passionate developer with expertise in modern web technologies.',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'Your City, Country',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ],
    certificates: [],
    products: [],
    services: [],
    testimonials: [],
    contact_messages: []
  }

  return {
    prepare: (query: string) => ({
      bind: (...params: any[]) => ({
        first: async () => {
          console.log('Mock DB Query:', query, 'Params:', params)
          // Simple mock implementation
          if (query.includes('SELECT') && query.includes('biography')) {
            return mockData.biography[0] || null
          }
          return null
        },
        all: async () => {
          console.log('Mock DB Query:', query, 'Params:', params)
          if (query.includes('biography')) return mockData.biography
          if (query.includes('certificates')) return mockData.certificates
          if (query.includes('products')) return mockData.products
          if (query.includes('services')) return mockData.services
          if (query.includes('testimonials')) return mockData.testimonials
          if (query.includes('contact_messages')) return mockData.contact_messages
          return []
        },
        run: async () => {
          console.log('Mock DB Query:', query, 'Params:', params)
          return { success: true, meta: { changes: 1, last_row_id: 1 } }
        }
      }),
      first: async () => {
        console.log('Mock DB Query:', query)
        return null
      },
      all: async () => {
        console.log('Mock DB Query:', query)
        return []
      },
      run: async () => {
        console.log('Mock DB Query:', query)
        return { success: true }
      }
    }),
    exec: async (query: string) => {
      console.log('Mock DB Exec:', query)
      return { success: true }
    }
  }
}

export async function initializeDatabase(db: DatabaseConnection) {
  try {
    // Read and execute schema
    const schema = `
      CREATE TABLE IF NOT EXISTS biography (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        full_name TEXT NOT NULL,
        title TEXT,
        summary TEXT,
        description TEXT,
        email TEXT,
        phone TEXT,
        location TEXT,
        website TEXT,
        linkedin_url TEXT,
        github_url TEXT,
        twitter_url TEXT,
        pdf_url TEXT,
        profile_image_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS certificates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        issuer TEXT NOT NULL,
        issue_date DATE NOT NULL,
        expiry_date DATE,
        credential_id TEXT,
        credential_url TEXT,
        file_url TEXT,
        description TEXT,
        skills TEXT,
        is_featured BOOLEAN DEFAULT FALSE,
        display_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        short_description TEXT,
        price DECIMAL(10,2),
        currency TEXT DEFAULT 'USD',
        image_url TEXT,
        gallery_urls TEXT,
        purchase_link TEXT,
        demo_link TEXT,
        github_link TEXT,
        category TEXT,
        tags TEXT,
        features TEXT,
        is_featured BOOLEAN DEFAULT FALSE,
        is_available BOOLEAN DEFAULT TRUE,
        display_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        short_description TEXT,
        price_range TEXT,
        price_from DECIMAL(10,2),
        price_to DECIMAL(10,2),
        currency TEXT DEFAULT 'USD',
        icon_name TEXT,
        image_url TEXT,
        category TEXT,
        duration TEXT,
        deliverables TEXT,
        features TEXT,
        is_featured BOOLEAN DEFAULT FALSE,
        is_available BOOLEAN DEFAULT TRUE,
        display_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS testimonials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        author TEXT NOT NULL,
        role TEXT,
        company TEXT,
        avatar_url TEXT,
        quote TEXT NOT NULL,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        project_name TEXT,
        service_category TEXT,
        date DATE,
        is_featured BOOLEAN DEFAULT FALSE,
        is_approved BOOLEAN DEFAULT TRUE,
        display_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS contact_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        subject TEXT,
        message TEXT NOT NULL,
        phone TEXT,
        company TEXT,
        service_interest TEXT,
        status TEXT DEFAULT 'new',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `

    await db.exec(schema)
    console.log('Database initialized successfully')
    return { success: true }
  } catch (error) {
    console.error('Database initialization failed:', error)
    return { success: false, error: error.message }
  }
}
