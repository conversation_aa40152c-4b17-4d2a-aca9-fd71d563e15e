import React from 'react'
import { BarChart3, Users, Package, Award, MessageSquare, TrendingUp } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import Card from '@/components/ui/Card'

const AdminDashboard: React.FC = () => {
  const { t } = useTranslation()

  const stats = [
    {
      icon: Award,
      label: 'الشهادات',
      value: '12',
      change: '+2',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: Package,
      label: 'المنتجات',
      value: '8',
      change: '+1',
      color: 'text-green-600 bg-green-100'
    },
    {
      icon: Users,
      label: 'الخدمات',
      value: '6',
      change: '0',
      color: 'text-purple-600 bg-purple-100'
    },
    {
      icon: MessageSquare,
      label: 'الرسائل',
      value: '24',
      change: '+5',
      color: 'text-orange-600 bg-orange-100'
    }
  ]

  const recentActivity = [
    { action: 'تم إضافة شهادة جديدة', time: 'منذ ساعتين', type: 'certificate' },
    { action: 'رسالة جديدة من عميل', time: 'منذ 4 ساعات', type: 'message' },
    { action: 'تم تحديث معلومات المنتج', time: 'أمس', type: 'product' },
    { action: 'تم إضافة توصية جديدة', time: 'منذ يومين', type: 'testimonial' }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-secondary-900 mb-2">
          مرحباً بك في لوحة الإدارة
        </h2>
        <p className="text-secondary-600">
          إليك نظرة عامة على موقعك الشخصي
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} hover className="relative overflow-hidden">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600 mb-1">
                  {stat.label}
                </p>
                <p className="text-3xl font-bold text-secondary-900">
                  {stat.value}
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp size={16} className="text-green-500 ml-1" />
                  <span className="text-sm text-green-600 font-medium">
                    {stat.change}
                  </span>
                  <span className="text-sm text-secondary-500 mr-1">
                    هذا الشهر
                  </span>
                </div>
              </div>
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${stat.color}`}>
                <stat.icon size={24} />
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <h3 className="text-lg font-bold text-secondary-900 mb-4">
            النشاط الأخير
          </h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg">
                <div className="w-2 h-2 bg-primary-600 rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-secondary-900">
                    {activity.action}
                  </p>
                  <p className="text-xs text-secondary-500">
                    {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card>
          <h3 className="text-lg font-bold text-secondary-900 mb-4">
            إجراءات سريعة
          </h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 bg-primary-50 hover:bg-primary-100 rounded-lg text-right transition-colors group">
              <Award className="text-primary-600 mb-2 group-hover:scale-110 transition-transform" size={24} />
              <p className="text-sm font-medium text-primary-700">إضافة شهادة</p>
            </button>
            <button className="p-4 bg-green-50 hover:bg-green-100 rounded-lg text-right transition-colors group">
              <Package className="text-green-600 mb-2 group-hover:scale-110 transition-transform" size={24} />
              <p className="text-sm font-medium text-green-700">إضافة منتج</p>
            </button>
            <button className="p-4 bg-purple-50 hover:bg-purple-100 rounded-lg text-right transition-colors group">
              <Users className="text-purple-600 mb-2 group-hover:scale-110 transition-transform" size={24} />
              <p className="text-sm font-medium text-purple-700">إضافة خدمة</p>
            </button>
            <button className="p-4 bg-orange-50 hover:bg-orange-100 rounded-lg text-right transition-colors group">
              <MessageSquare className="text-orange-600 mb-2 group-hover:scale-110 transition-transform" size={24} />
              <p className="text-sm font-medium text-orange-700">عرض الرسائل</p>
            </button>
          </div>
        </Card>
      </div>

      {/* Chart Placeholder */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-secondary-900">
            إحصائيات الموقع
          </h3>
          <BarChart3 className="text-secondary-400" size={24} />
        </div>
        <div className="h-64 bg-secondary-50 rounded-lg flex items-center justify-center">
          <div className="text-center text-secondary-500">
            <BarChart3 size={48} className="mx-auto mb-2" />
            <p>الرسوم البيانية ستظهر هنا</p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default AdminDashboard
