// Arabic translations
export const ar = {
  // Navigation
  nav: {
    home: 'الرئيسية',
    about: 'نبذة عني',
    certificates: 'الشهادات',
    products: 'المنتجات',
    services: 'الخدمات',
    testimonials: 'التوصيات',
    contact: 'تواصل معي',
    admin: 'لوحة الإدارة',
    login: 'تسجيل الدخول',
    logout: 'تسجيل الخروج'
  },

  // Common
  common: {
    loading: 'جاري التحميل...',
    error: 'حدث خطأ',
    success: 'تم بنجاح',
    save: 'حفظ',
    cancel: 'إلغاء',
    delete: 'حذف',
    edit: 'تعديل',
    add: 'إضافة',
    view: 'عرض',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    submit: 'إرسال',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    name: 'الاسم',
    email: 'ال<PERSON><PERSON>ي<PERSON> الإلكتروني',
    phone: 'الهاتف',
    message: 'الرسالة',
    date: 'التاريخ',
    status: 'الحالة',
    actions: 'الإجراءات',
    required: 'مطلوب',
    optional: 'اختياري',
    yes: 'نعم',
    no: 'لا',
    confirm: 'تأكيد',
    close: 'إغلاق'
  },

  // Home page
  home: {
    hero: {
      title: 'مرحباً، أنا',
      subtitle: 'مطور ومصمم شغوف',
      description: 'أقوم بإنشاء تجارب رقمية استثنائية تجمع بين التصميم الجميل والتكنولوجيا المتطورة',
      cta: 'تعرف على أعمالي',
      contact: 'تواصل معي'
    },
    about: {
      title: 'نبذة عني',
      subtitle: 'تعرف على قصتي ومهاراتي',
      viewCv: 'عرض السيرة الذاتية',
      downloadCv: 'تحميل السيرة الذاتية'
    },
    certificates: {
      title: 'الشهادات',
      subtitle: 'شهاداتي المهنية والأكاديمية',
      viewAll: 'عرض جميع الشهادات'
    },
    products: {
      title: 'المنتجات',
      subtitle: 'مشاريعي ومنتجاتي الرقمية',
      viewAll: 'عرض جميع المنتجات',
      demo: 'معاينة',
      github: 'الكود المصدري',
      purchase: 'شراء'
    },
    services: {
      title: 'الخدمات',
      subtitle: 'الخدمات التي أقدمها',
      viewAll: 'عرض جميع الخدمات',
      startingFrom: 'ابتداءً من'
    },
    testimonials: {
      title: 'آراء العملاء',
      subtitle: 'ماذا يقول عملائي عن عملي',
      viewAll: 'عرض جميع التوصيات'
    },
    contact: {
      title: 'تواصل معي',
      subtitle: 'لنبدأ مشروعك القادم معاً',
      form: {
        name: 'الاسم الكامل',
        email: 'البريد الإلكتروني',
        subject: 'الموضوع',
        message: 'الرسالة',
        company: 'الشركة',
        service: 'الخدمة المطلوبة',
        send: 'إرسال الرسالة'
      },
      info: {
        email: 'البريد الإلكتروني',
        phone: 'الهاتف',
        location: 'الموقع',
        social: 'وسائل التواصل'
      }
    }
  },

  // Admin panel
  admin: {
    title: 'لوحة الإدارة',
    dashboard: 'لوحة التحكم',
    biography: 'السيرة الذاتية',
    certificates: 'الشهادات',
    products: 'المنتجات',
    services: 'الخدمات',
    testimonials: 'التوصيات',
    messages: 'الرسائل',
    settings: 'الإعدادات',
    
    // Forms
    forms: {
      biography: {
        fullName: 'الاسم الكامل',
        title: 'المسمى الوظيفي',
        summary: 'نبذة مختصرة',
        description: 'الوصف التفصيلي',
        email: 'البريد الإلكتروني',
        phone: 'رقم الهاتف',
        location: 'الموقع',
        website: 'الموقع الإلكتروني',
        linkedin: 'لينكد إن',
        github: 'جيت هاب',
        twitter: 'تويتر'
      },
      certificate: {
        title: 'عنوان الشهادة',
        issuer: 'الجهة المانحة',
        issueDate: 'تاريخ الإصدار',
        expiryDate: 'تاريخ الانتهاء',
        credentialId: 'رقم الشهادة',
        credentialUrl: 'رابط الشهادة',
        description: 'الوصف',
        skills: 'المهارات',
        featured: 'مميزة'
      },
      product: {
        name: 'اسم المنتج',
        description: 'الوصف',
        shortDescription: 'وصف مختصر',
        price: 'السعر',
        currency: 'العملة',
        purchaseLink: 'رابط الشراء',
        demoLink: 'رابط المعاينة',
        githubLink: 'رابط الكود المصدري',
        category: 'الفئة',
        tags: 'العلامات',
        features: 'المميزات',
        featured: 'مميز',
        available: 'متاح'
      },
      service: {
        name: 'اسم الخدمة',
        description: 'الوصف',
        shortDescription: 'وصف مختصر',
        priceRange: 'نطاق السعر',
        priceFrom: 'السعر من',
        priceTo: 'السعر إلى',
        currency: 'العملة',
        icon: 'الأيقونة',
        category: 'الفئة',
        duration: 'المدة',
        deliverables: 'المخرجات',
        features: 'المميزات',
        featured: 'مميزة',
        available: 'متاحة'
      },
      testimonial: {
        author: 'اسم العميل',
        role: 'المنصب',
        company: 'الشركة',
        quote: 'التوصية',
        rating: 'التقييم',
        projectName: 'اسم المشروع',
        serviceCategory: 'فئة الخدمة',
        date: 'التاريخ',
        featured: 'مميزة'
      }
    },

    // Messages
    messages: {
      new: 'جديدة',
      read: 'مقروءة',
      replied: 'تم الرد',
      archived: 'مؤرشفة',
      markAsRead: 'تحديد كمقروءة',
      markAsReplied: 'تحديد كمجاب عليها',
      archive: 'أرشفة'
    },

    // Notifications
    notifications: {
      saveSuccess: 'تم الحفظ بنجاح',
      deleteSuccess: 'تم الحذف بنجاح',
      updateSuccess: 'تم التحديث بنجاح',
      createSuccess: 'تم الإنشاء بنجاح',
      error: 'حدث خطأ، يرجى المحاولة مرة أخرى',
      confirmDelete: 'هل أنت متأكد من الحذف؟',
      unsavedChanges: 'لديك تغييرات غير محفوظة'
    }
  },

  // Validation messages
  validation: {
    required: 'هذا الحقل مطلوب',
    email: 'يرجى إدخال بريد إلكتروني صحيح',
    url: 'يرجى إدخال رابط صحيح',
    minLength: 'يجب أن يكون النص أطول من {min} أحرف',
    maxLength: 'يجب أن يكون النص أقصر من {max} حرف',
    number: 'يرجى إدخال رقم صحيح',
    positive: 'يجب أن يكون الرقم موجباً',
    date: 'يرجى إدخال تاريخ صحيح',
    phone: 'يرجى إدخال رقم هاتف صحيح'
  },

  // Error messages
  errors: {
    network: 'خطأ في الاتصال بالشبكة',
    server: 'خطأ في الخادم',
    notFound: 'الصفحة غير موجودة',
    unauthorized: 'غير مخول للوصول',
    forbidden: 'ممنوع الوصول',
    validation: 'خطأ في التحقق من البيانات',
    upload: 'خطأ في رفع الملف',
    generic: 'حدث خطأ غير متوقع'
  },

  // Success messages
  success: {
    messageSent: 'تم إرسال الرسالة بنجاح! سنتواصل معك قريباً',
    profileUpdated: 'تم تحديث الملف الشخصي بنجاح',
    itemCreated: 'تم إنشاء العنصر بنجاح',
    itemUpdated: 'تم تحديث العنصر بنجاح',
    itemDeleted: 'تم حذف العنصر بنجاح',
    fileUploaded: 'تم رفع الملف بنجاح'
  }
}
